import type { NavItemConfig } from "@/types/nav";
import { paths } from "@/paths";

// NOTE: We did not use React Components for Icons, because
//  you may want to get the config from the server.

// NOTE: First level of navItem elements are groups.

export interface DashboardConfig {
	// Overriden by Settings Context.
	layout: "horizontal" | "vertical";
	// Overriden by Settings Context.
	navColor: "blend_in" | "discrete" | "evident";
	navItems: NavItemConfig[];
}

export const dashboardConfig = {
	layout: "vertical",
	navColor: "discrete",
	navItems: [
		{
			key: "dashboards",
			title: "Dashboards",
			items: [{ key: "overview", title: "Overview", href: paths.dashboard.overview, icon: "house" }],
		},
		{
			key: "administration",
			title: "Administration",
			items: [
				{
					key: "jobs",
					title: "Jobs",
					icon: "calendar-check",
					items: [
						{ key: "list", title: "List", href: paths.administration.jobs.index()},
						{ key: "create", title: "Create", href: paths.administration.jobs.create },
					],
				}],
		},
		{
			key: "misc",
			title: "Misc",
			items: [
				{ key: "blank", title: "Blank", href: paths.dashboard.blank, icon: "file-dashed" },
			],
		},

	],
} satisfies DashboardConfig;
