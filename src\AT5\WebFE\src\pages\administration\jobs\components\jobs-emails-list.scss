// Jobs Emails List Component Styles

.jobs-emails-list {
  // Error tooltip styling
  &__error-tooltip {
    background-color: var(--mui-palette-error-main) !important;
    color: white !important;
    font-size: 0.75rem !important;
    max-width: 300px !important;
  }

  // Error message styling
  &__error-message {
    color: var(--mui-palette-error-main);
    font-size: 0.75rem;
    margin-top: 0.5rem;
  }

  // Email cell styling for editable text
  &__email-cell {
    cursor: text;
    width: 100%;
  }

  // Edit input container
  &__edit-input-container {
    width: 100%;
  }

  // DataGrid custom styling
  &__data-grid {
    margin-top: 1rem;
    border: 1px solid #e0e0e0;

    // Column headers
    .MuiDataGrid-columnHeaders {
      background-color: #f5f5f5;
      border-bottom: 1px solid #e0e0e0;
      font-size: 0.875rem;
      font-weight: 500;
    }

    .MuiDataGrid-columnHeader {
      padding: 8px 12px;
    }

    // Cells
    .MuiDataGrid-cell {
      padding: 8px 12px;
      font-size: 0.875rem;
      border-bottom: 1px solid #f0f0f0;
      display: flex;
      align-items: center;

      &:focus {
        outline: none;
      }

      &:focus-within {
        outline: none;
      }
    }

    // Rows
    .MuiDataGrid-row {
      &:hover {
        background-color: #fafafa;
      }

      &:last-child .MuiDataGrid-cell {
        border-bottom: none;
      }
    }

    // Footer
    .MuiDataGrid-footerContainer {
      border-top: 1px solid #e0e0e0;
      background-color: #fafafa;
    }

    // Hide column separators
    .MuiDataGrid-columnSeparator {
      display: none;
    }
  }
}
