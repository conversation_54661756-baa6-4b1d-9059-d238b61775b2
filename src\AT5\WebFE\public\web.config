<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <system.webServer>
    <!-- Handle client-side routing -->
    <rewrite>
      <rules>
        <rule name="React Routes" stopProcessing="true">
          <match url="app/.*" />
          <conditions logicalGrouping="MatchAll">
            <add input="{REQUEST_FILENAME}" matchType="IsFile" negate="true" />
            <add input="{REQUEST_FILENAME}" matchType="IsDirectory" negate="true" />
            <add input="{REQUEST_URI}" pattern="^/(api)" negate="true" />
          </conditions>
          <action type="Rewrite" url="/app/" />
        </rule>
      </rules>
    </rewrite>

    <!-- Browser caching -->
    <staticContent>
      <!-- Cache static assets with hash for 1 year -->
      <clientCache cacheControlMode="UseMaxAge" cacheControlMaxAge="365.00:00:00" />
    </staticContent>

    <!-- Custom headers for different file types -->
    <httpProtocol>
      <customHeaders>
        <clear />
      </customHeaders>
    </httpProtocol>

    <!-- Compression -->
    <httpCompression>
      <dynamicTypes>
        <clear />
        <add enabled="true" mimeType="text/*" />
        <add enabled="true" mimeType="message/*" />
        <add enabled="true" mimeType="application/javascript" />
        <add enabled="true" mimeType="application/json" />
        <add enabled="true" mimeType="*/*" />
      </dynamicTypes>
      <staticTypes>
        <clear />
        <add enabled="true" mimeType="text/*" />
        <add enabled="true" mimeType="message/*" />
        <add enabled="true" mimeType="application/javascript" />
        <add enabled="true" mimeType="application/atom+xml" />
        <add enabled="true" mimeType="application/xaml+xml" />
        <add enabled="true" mimeType="image/svg+xml" />
        <add enabled="true" mimeType="*/*" />
      </staticTypes>
    </httpCompression>

    <!-- Specific caching rules -->
    <location path="assets">
      <system.webServer>
        <staticContent>
          <clientCache cacheControlMode="UseMaxAge" cacheControlMaxAge="365.00:00:00" />
        </staticContent>
        <httpProtocol>
          <customHeaders>
            <add name="Cache-Control" value="public, max-age=31536000, immutable" />
          </customHeaders>
        </httpProtocol>
      </system.webServer>
    </location>

    <location path="favicon.ico">
      <system.webServer>
        <staticContent>
          <clientCache cacheControlMode="UseMaxAge" cacheControlMaxAge="7.00:00:00" />
        </staticContent>
        <httpProtocol>
          <customHeaders>
            <add name="Cache-Control" value="public, max-age=604800" />
          </customHeaders>
        </httpProtocol>
      </system.webServer>
    </location>

    <location path="index.html">
      <system.webServer>
        <staticContent>
          <clientCache cacheControlMode="DisableCache" />
        </staticContent>
        <httpProtocol>
          <customHeaders>
            <add name="Cache-Control" value="no-cache, no-store, must-revalidate" />
            <add name="Pragma" value="no-cache" />
            <add name="Expires" value="0" />
          </customHeaders>
        </httpProtocol>
      </system.webServer>
    </location>
  </system.webServer>
</configuration>
