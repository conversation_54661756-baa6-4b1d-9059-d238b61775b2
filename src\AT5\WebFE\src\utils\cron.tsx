import dayjs, { Dayjs } from "dayjs";

type ANY_CRON = '*';

type CronSeconds = '0' | ANY_CRON;

type SingleDigit = '0' | '1' | '2' | '3' | '4' | '5' | '6' | '7' | '8' | '9';
type TensDigitMinutes = '1' | '2' | '3' | '4' | '5';
export type CronMinuteOptions = SingleDigit | `${TensDigitMinutes}${SingleDigit}`;
type CronMinutes =
    ANY_CRON 
    | CronMinuteOptions
    | `${CronMinuteOptions}/${CronMinuteOptions}`;

export type Hours = `${0 | 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 11 | 12 |
            13 | 14 | 15 | 16 | 17 | 18 | 19 | 20 | 21 | 22 | 23}`;

type CronHours = 
    ANY_CRON
    | Hours
    | `${Hours}-${Hours | "24"}`;

export type CronDayOfMonth = ANY_CRON | `${1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10 |
                   11 | 12 | 13 | 14 | 15 | 16 | 17 | 18 | 19 | 20 |
                   21 | 22 | 23 | 24 | 25 | 26 | 27 | 28 | 29 | 30 | 31}`;

export type CronMonth = ANY_CRON | `${0 | 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 11 }`;

type CronDayOfWeek = ANY_CRON | `${ 1 | 2 | 3 | 4 | 5 | 6 | 7}`;

export type CronYear = string;

export type CronArray = [CronSeconds, CronMinutes, CronHours, CronDayOfMonth, CronMonth, CronDayOfWeek, CronYear];

export type Periodicity = "YEAR" | "MONTH" | "WEEK" | "DAY" | "HOUR" | "MINUTES" 

interface IsInRangeProps{
    part: string,
    min: number,
    max: number
}

function isInRange({part, min, max}: IsInRangeProps): boolean{
    let value = parseInt(part);

    return min <= value && value <= max;
}

function isValidCronSeconds(part: string): part is CronSeconds {
    return ['0', '*'].includes(part);
}

export function isValidCronMinuteOptions(part: string): part is CronMinuteOptions {
    return isInRange({part, min: 0, max: 59});
}

function isValidCronMinutes(part: string): part is CronMinutes {
    if (!part?.includes('/')){
        return isValidCronMinuteOptions(part) || part === '*';
    }

    let parts = part.split('/');

    return parts.length === 2 && isValidCronMinuteOptions(parts[0]) && isValidCronMinuteOptions(parts[1]);
}

export function isValidHours(part: string): part is Hours {
    return isInRange({part, min: 0, max: 24});
}

function isValidCronHours(part: string): part is CronHours {
    if (!part.includes('-')){
        return isValidHours(part) || part === '*';
    }

    let parts = part.split('-');

    return parts.length === 2 && isValidHours(parts[0]) && isValidHours(parts[1]);
}

export function isValidCronDayOfMonth(part: string): part is CronDayOfMonth {
    if (part === '*'){
        return true;
    }
    
    return isInRange({part, min: 1, max: 31});
}

function isValidCronMonth(part: string): part is CronMonth {
    if (part === '*'){
        return true;
    }
    
    return isInRange({part, min: 0, max: 11});
}

export function isValidCronDayOfWeek(part: string): part is CronDayOfWeek {
    if (part === '*'){
        return true;
    }
    
    return isInRange({part, min: 1, max: 7});
}

function isValidCronYear(part: string): part is CronYear {
    if (part === '*'){
        return true;
    }
    
    return isInRange({part, min: 1970, max: 2099});
}

export function isValidCronArray(parts: string[]): parts is CronArray {
    if (parts.length !== 7) {
        return false;
    }

    return isValidCronSeconds(parts[0])
        && isValidCronMinutes(parts[1])
        && isValidCronHours(parts[2])
        && isValidCronDayOfMonth(parts[3])
        && isValidCronMonth(parts[4])
        && isValidCronDayOfWeek(parts[5])
        && isValidCronYear(parts[6]);
}

export class CronSingle {
    readonly seconds: CronSeconds;
    readonly minutes: CronMinutes;
    readonly hours: CronHours;
    readonly dayOfMonth: CronDayOfMonth;
    readonly month: CronMonth;
    readonly dayOfWeek: CronDayOfWeek;
    readonly year: string;

    constructor(
        seconds: CronSeconds,
        minutes: CronMinutes,
        hours: CronHours,
        dayOfMonth: CronDayOfMonth,
        month: CronMonth,
        dayOfWeek: CronDayOfWeek,
        year: string
    ) {
        const dt = dayjs();

        this.seconds = '0';
        this.minutes = minutes === '*' || minutes?.includes('/')
            ? `${dt.minute()}` as CronMinutes
            : minutes;
        this.hours = hours === '*' || hours?.includes('-')
            ? `${dt.hour()}` as CronHours
            : hours;
        this.dayOfMonth = dayOfMonth === '*' ? `${dt.date()}` as CronDayOfMonth : dayOfMonth;
        this.month = month === '*' ? `${dt.month()}` as CronMonth : month;
        this.dayOfWeek = dayOfWeek === '*' ? '1' as CronDayOfWeek : dayOfWeek;
        this.year = year === '*' ? `${dt.year()}` : year;
    }

    public equals(other: CronSingle): boolean {
        if (other instanceof CronSingle) {
            return this.toString() === other.toString();
        }

        return false;
    }

    public copyWith(
        overrides: Partial<CronSingle>
    ): CronSingle {
        return new CronSingle(
            overrides.seconds ?? this.seconds,
            overrides.minutes ?? this.minutes,
            overrides.hours ?? this.hours,
            overrides.dayOfMonth ?? this.dayOfMonth,
            overrides.month ?? this.month,
            overrides.dayOfWeek ?? this.dayOfWeek,
            overrides.year ?? this.year
        );
    }

    static parse(expr: string): CronSingle | undefined {
        const parts = expr.replace(/\?/g, "*").split(/\s+/);

        if (expr?.includes(',') || !isValidCronArray(parts) || parts[0] != '0' 
            || parts[5] != '*' || !parts.every((val, idx) => idx === 5 || val !== '*')){
            return undefined;
        }

        return new CronSingle(parts[0], parts[1], parts[2], parts[3], parts[4], parts[5], parts[6]);
    }

    static parseArray(parts: CronArray): CronSingle | undefined {

        if (parts[0] != '0' || parts[5] != '*' || !parts.every((val, idx) => idx === 5 || val !== '*')){
            return undefined;
        }

        return new CronSingle(parts[0], parts[1], parts[2], parts[3], parts[4], parts[5], parts[6]);
    }

    public toCronArray(): CronArray{
        return ["0", this.minutes, this.hours, this.dayOfMonth, this.month, "*", this.year];
    }

    public toString(): string {
        return this.toCronArray().join(' ');
    }
}

export class CronRepeated{
    readonly seconds: CronSeconds;
    readonly minutes: CronMinutes;
    readonly hours: CronHours;
    readonly dayOfMonth: CronDayOfMonth;
    readonly month: CronMonth;
    readonly dayOfWeek: CronDayOfWeek;
    readonly year: CronYear;
    readonly periodicity: Periodicity;
    readonly x: CronMinuteOptions;
    readonly start: CronMinuteOptions;
    readonly timeRange: [Hours, Hours | "24"];

    constructor(
        [seconds, minutes, hours, dayOfMonth, month, dayOfWeek, year] : CronArray,
        periodicity: Periodicity,
        x: CronMinuteOptions,
        start: CronMinuteOptions,
        timeRange: [Hours, Hours | "24"]
    ) {
        const dt = dayjs();

        this.seconds = '0';
        this.minutes = minutes === '*' || minutes?.includes('/')
            ? `${dt.minute()}` as CronMinutes
            : minutes;
        this.hours = hours === '*' || hours?.includes('-')
            ? `${dt.hour()}` as CronHours
            : hours;
        this.dayOfMonth = dayOfMonth === '*' ? `${dt.date()}` as CronDayOfMonth : dayOfMonth;
        this.month = month === '*' ? `${dt.month()}` as CronMonth : month;
        this.dayOfWeek = dayOfWeek === '*' ? '1' as CronDayOfWeek : dayOfWeek;
        this.year = year === '*' ? `${dt.year()}` : year;

        this.periodicity = periodicity;
        this.x = x;
        this.start = start;
        this.timeRange = timeRange;
    }


    public copyWith(
        overrides: Partial<CronRepeated>
    ): CronRepeated {
        return new CronRepeated(
            [overrides.seconds ?? this.seconds,
            overrides.minutes ?? this.minutes,
            overrides.hours ?? this.hours,
            overrides.dayOfMonth ?? this.dayOfMonth,
            overrides.month ?? this.month,
            overrides.dayOfWeek ?? this.dayOfWeek,
            overrides.year ?? this.year],
            overrides.periodicity ?? this.periodicity,
            overrides.x ?? this.x,
            overrides.start ?? this.start,
            overrides.timeRange ?? this.timeRange
        );
    }
    
    private static cronPartsToPeriodicity(parts: CronArray): Periodicity | undefined {
        const starCount = parts.reduce((acc, el) => (el === '*' ? acc + 1 : acc), 0);
        if (starCount < 2 || starCount > 5) return undefined;
        if (starCount === 2) return "YEAR";
        if (starCount === 3 && parts[5] != '*') return "WEEK";
        if (starCount === 3 && parts[5] === '*') return "MONTH";
        if (parts[1]?.includes('/')) return "MINUTES";
        if (starCount  ===  4) return "DAY";
        if (starCount  ===  5) return "HOUR";
        return undefined;
    }

    private static cronPartsToXStartRange(parts: CronArray): {
        x: CronMinuteOptions, 
        start: CronMinuteOptions, 
        range: [Hours, Hours | "24"]
    } {
        let x = "1" as CronMinuteOptions;
        let start = "0" as CronMinuteOptions;
        let range = ["0", "24"] as [Hours, Hours | "24"];
        if (parts[1]?.includes('/')){
            let minuteParts = parts[1].split('/');
            if (isValidCronMinuteOptions(minuteParts[0]) && isValidCronMinuteOptions(minuteParts[1])){
                start = minuteParts[0];
                x = minuteParts[1];
            }
        }

        if (parts[2]?.includes('-')){
            let hourParts = parts[2].split('-')
            if (isValidHours(hourParts[0]) && isValidHours(hourParts[1])){
                range = [hourParts[0], hourParts[1]] as [Hours, Hours | "24"];
            }
        }
        else if (parts[2] != '*' && isValidHours(parts[2])){
            range = [parts[2], parts[2]] as [Hours, Hours | "24"];
        }

        return {x, start, range: range}
    }

    static parse(expr: string): CronRepeated | undefined {
        const parts = expr.replace(/\?/g, "*").split(/\s+/);
        
        if (parts[0] != '0' || expr?.includes(',') || !isValidCronArray(parts)){
            return undefined;
        }

        let periodicityTmp = CronRepeated.cronPartsToPeriodicity(parts);
        if (periodicityTmp === undefined){
            return undefined;
        }

        let {x, start, range} = this.cronPartsToXStartRange(parts);

        return new CronRepeated(
            [parts[0],
            parts[1],
            parts[2],
            parts[3],
            parts[4],
            parts[5],
            parts[6]],
            periodicityTmp,
            x,
            start,
            range
        )
    }

    static parseArray(parts: CronArray): CronRepeated | undefined {        
        if (parts[0] != '0' || parts?.includes(',') || !isValidCronArray(parts)){
            return undefined;
        }

        let periodicityTmp = CronRepeated.cronPartsToPeriodicity(parts);
        if (periodicityTmp === undefined){
            return undefined;
        }

        let {x, start, range} = this.cronPartsToXStartRange(parts);

        return new CronRepeated(
            [parts[0],
            parts[1],
            parts[2],
            parts[3],
            parts[4],
            parts[5],
            parts[6]],
            periodicityTmp,
            x,
            start,
            range
        )
    }

    public toCronArray(): CronArray{
        const cronFields: CronArray = ["0", "*", "*", "*", "*", "*", "*"]; // Always 0th second
        switch (this.periodicity) {
            case "YEAR":
                cronFields[1] = this.minutes;
                cronFields[2] = this.hours;
                cronFields[3] = this.dayOfMonth;
                cronFields[4] = this.month;
                break;
              
            case "MONTH":
                cronFields[1] = this.minutes;
                cronFields[2] = this.hours;
                cronFields[3] = this.dayOfMonth;
                break;
              
            case "WEEK":
                cronFields[1] = this.minutes;
                cronFields[2] = this.hours;
                cronFields[5] = this.dayOfWeek;
                break;

            case "DAY":
                cronFields[1] = this.minutes;
                cronFields[2] = this.hours;  
                break;

            case "HOUR":
                cronFields[1] = this.minutes;
                break;

            case "MINUTES":
                cronFields[1] = `${this.start}/${this.x}`;
                if (parseInt(this.timeRange[1]) - parseInt(this.timeRange[0]) === 24){
                    cronFields[2] = '*';
                    break;
                }

                if (this.timeRange[0] === this.timeRange[1]){
                    // TS has a problem with this, but otherwise we allow for an invalid hours value to be displayed.
                    cronFields[2] = this.timeRange[1] === "24" ? "0" : this.timeRange[1];
                    break;
                }
                cronFields[2] =`${this.timeRange[0]}-${this.timeRange[1] === "24" ? "0" : this.timeRange[1]}`;
                break;
        }

        return cronFields;
    }

    public toString(): string {
        return this.toCronArray().join(' ');
    }
}

interface CronToDateTimeProps {
  minutes: string;
  hours: string;
  dayOfMonth: string;
  month: string;
  year: string;
}

export function CronToDateTime(parts: CronToDateTimeProps): Dayjs {
    let dt = dayjs();
    dt.minute(parseInt(parts.minutes));
    dt.hour(parseInt(parts.hours));
    dt.date(parseInt(parts.dayOfMonth));
    dt.month(parseInt(parts.month));
    dt.year(parseInt(parts.year));
    return dt;
}