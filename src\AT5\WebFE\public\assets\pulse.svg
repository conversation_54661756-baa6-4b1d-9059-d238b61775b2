<svg width="435" height="412" viewBox="0 0 435 412" fill="none" xmlns="http://www.w3.org/2000/svg">
  <g opacity="0.2">
    <g filter="url(#filter0_d_13910_17089)">
      <circle cx="37.3079" cy="37.9744" r="280.703" transform="rotate(135 37.3079 37.9744)"
        fill="url(#paint0_linear_13910_17089)" shape-rendering="crispEdges" />
      <circle cx="37.3079" cy="37.9744" r="280.328" transform="rotate(135 37.3079 37.9744)"
        stroke="url(#paint1_linear_13910_17089)" stroke-width="0.75" shape-rendering="crispEdges" />
    </g>
    <g filter="url(#filter1_d_13910_17089)">
      <circle cx="37.3075" cy="37.9746" r="183.067" transform="rotate(135 37.3075 37.9746)"
        fill="url(#paint2_linear_13910_17089)" shape-rendering="crispEdges" />
      <circle cx="37.3075" cy="37.9746" r="182.692" transform="rotate(135 37.3075 37.9746)"
        stroke="url(#paint3_linear_13910_17089)" stroke-width="0.75" shape-rendering="crispEdges" />
    </g>
    <g opacity="0.5" filter="url(#filter2_d_13910_17089)">
      <circle cx="37.3073" cy="37.9741" r="89.4996" transform="rotate(135 37.3073 37.9741)"
        fill="url(#paint4_linear_13910_17089)" shape-rendering="crispEdges" />
      <circle cx="37.3073" cy="37.9741" r="89.1246" transform="rotate(135 37.3073 37.9741)"
        stroke="url(#paint5_linear_13910_17089)" stroke-width="0.75" shape-rendering="crispEdges" />
    </g>
  </g>
  <defs>
    <filter id="filter0_d_13910_17089" x="-257.395" y="-253.729" width="589.406" height="589.407"
      filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix" />
      <feColorMatrix in="SourceAlpha" type="matrix"
        values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha" />
      <feOffset dy="3" />
      <feGaussianBlur stdDeviation="7" />
      <feComposite in2="hardAlpha" operator="out" />
      <feColorMatrix type="matrix"
        values="0 0 0 0 0.620833 0 0 0 0 0.620833 0 0 0 0 0.620833 0 0 0 0.03 0" />
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_13910_17089" />
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_13910_17089" result="shape" />
    </filter>
    <filter id="filter1_d_13910_17089" x="-159.76" y="-156.093" width="394.135" height="394.135"
      filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix" />
      <feColorMatrix in="SourceAlpha" type="matrix"
        values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha" />
      <feOffset dy="3" />
      <feGaussianBlur stdDeviation="7" />
      <feComposite in2="hardAlpha" operator="out" />
      <feColorMatrix type="matrix"
        values="0 0 0 0 0.620833 0 0 0 0 0.620833 0 0 0 0 0.620833 0 0 0 0.03 0" />
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_13910_17089" />
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_13910_17089" result="shape" />
    </filter>
    <filter id="filter2_d_13910_17089" x="-66.1924" y="-62.5254" width="206.999" height="206.999"
      filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix" />
      <feColorMatrix in="SourceAlpha" type="matrix"
        values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha" />
      <feOffset dy="3" />
      <feGaussianBlur stdDeviation="7" />
      <feComposite in2="hardAlpha" operator="out" />
      <feColorMatrix type="matrix"
        values="0 0 0 0 0.620833 0 0 0 0 0.620833 0 0 0 0 0.620833 0 0 0 0.03 0" />
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_13910_17089" />
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_13910_17089" result="shape" />
    </filter>
    <linearGradient id="paint0_linear_13910_17089" x1="37.3079" y1="-288.496" x2="37.3079"
      y2="17.3206" gradientUnits="userSpaceOnUse">
      <stop offset="0.00508045" stop-color="white" />
      <stop offset="1" stop-color="#F5F5F5" stop-opacity="0" />
    </linearGradient>
    <linearGradient id="paint1_linear_13910_17089" x1="45.6985" y1="-248.831" x2="28.9173"
      y2="125.694" gradientUnits="userSpaceOnUse">
      <stop stop-color="#ECECEC" />
      <stop offset="1" stop-color="#F9F9F9" />
    </linearGradient>
    <linearGradient id="paint2_linear_13910_17089" x1="37.3075" y1="-174.941" x2="37.3075"
      y2="24.5048" gradientUnits="userSpaceOnUse">
      <stop offset="0.00508045" stop-color="white" />
      <stop offset="1" stop-color="#F5F5F5" stop-opacity="0" />
    </linearGradient>
    <linearGradient id="paint3_linear_13910_17089" x1="42.7796" y1="-149.072" x2="31.8354"
      y2="95.1832" gradientUnits="userSpaceOnUse">
      <stop stop-color="#ECECEC" />
      <stop offset="1" stop-color="#F9F9F9" />
    </linearGradient>
    <linearGradient id="paint4_linear_13910_17089" x1="37.3073" y1="-66.1178" x2="37.3073"
      y2="31.3889" gradientUnits="userSpaceOnUse">
      <stop offset="0.00508045" stop-color="white" />
      <stop offset="1" stop-color="#F5F5F5" stop-opacity="0" />
    </linearGradient>
    <linearGradient id="paint5_linear_13910_17089" x1="39.9826" y1="-53.4711" x2="37.3074"
      y2="47.0758" gradientUnits="userSpaceOnUse">
      <stop stop-color="#D5D5D5" />
      <stop offset="1" stop-color="#F9F9F9" />
    </linearGradient>
  </defs>
</svg>