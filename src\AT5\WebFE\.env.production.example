# Production Environment Configuration
# Copy this to .env.production for production deployment

# App
VITE_APP_URL=
VITE_APP_BASE_PATH=/app
VITE_LOG_LEVEL=ERROR
VITE_AUTH_STRATEGY=NONE

# API Configuration for Production left intentionally vacant -- AT5 currently does not call API Service directly.

# 401 Redirect Configuration for Production
# Redirect to AT4 login page
VITE_NOT_AUTHORIZED_REDIRECT_PATH=/login

# Auth0
VITE_AUTH0_DOMAIN=
VITE_AUTH0_CLIENT_ID=

# Clerk
VITE_CLERK_PUBLISHABLE_KEY=

# Cognito
VITE_COGNITO_AUTHORITY=
VITE_COGNITO_DOMAIN=
VITE_COGNITO_CLIENT_ID=

# Supabase
VITE_SUPABASE_URL=
VITE_SUPABASE_PUBLIC_KEY=

# Mapbox
VITE_MAPBOX_API_KEY=

# Google Tag Manager
VITE_GOOGLE_TAG_MANAGER_ID=
