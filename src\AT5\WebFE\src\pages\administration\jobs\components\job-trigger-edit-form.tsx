"use client";

import * as React from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { Card, FormControl, FormControlLabel, FormHelperText, Grid, InputLabel, Link, OutlinedInput, Tooltip } from "@mui/material";
import Button from "@mui/material/Button";
import Paper from "@mui/material/Paper";
import Stack from "@mui/material/Stack";
import Switch from "@mui/material/Switch";
import Typography from "@mui/material/Typography";

import { CronEditSelector } from "@/pages/administration/jobs/components/cron-edit-selector";
import { Controller, useForm } from "react-hook-form";

import { z as zod } from "zod";
import { useGetJobTriggerQuery, useCreateJobTriggerMutation, useUpdateJobTriggerMutation, AtApiServiceEndpointsAdministrationModelsJobTriggerDto } from "@/store/api/administration";
import { useNavigate } from "react-router-dom";
import { toast } from "@/components/core/toaster";
import { paths } from "@/paths";
import { logger } from "@/lib/default-logger";
import { isValidCronArray } from "@/utils/cron";

const schema = zod.object({
    name: zod.string().min(1, "Name is required").max(255),
    type: zod.number().gte(0),
    description: zod.string().max(255).optional(),
    parameters: zod.string().min(1, "Parameters are required").max(255),
    disabled: zod.boolean(),
    runParameters: zod.string().max(512).optional(),
});

type Values = zod.infer<typeof schema>;

const defaultValues = {
    name: "",
    type: 1,
    description: "",
    parameters: "0 0 14 1 2 * 2025",
    disabled: false,
    runParameters: "",
} satisfies Values;

interface JobTriggerEditFormProps {
    jobId: number;
    triggerId?: number;
}

export function JobTriggerEditForm({ jobId, triggerId }: JobTriggerEditFormProps): React.JSX.Element{
    const [createJobTrigger, createOptions] = useCreateJobTriggerMutation();
    const [updateJobTrigger, updateOptions] = useUpdateJobTriggerMutation();
    const navigate = useNavigate();
    
    const {
        control,
        handleSubmit,
        formState: { errors },
        setValue,
        watch,
        reset,
    } = useForm<Values>({ defaultValues, resolver: zodResolver(schema) });

    const { data: triggerData, error: triggerError, isLoading: isLoadingTrigger } = useGetJobTriggerQuery({
        rootEntityId: `${jobId}`,
        subEntityId1: `${triggerId}`
    }, {skip: !triggerId});    
    
    // Handle job trigger data loading for edit mode
    React.useEffect(() => {
        if (triggerId && triggerData?.entity?.id) {
            const formData: Values = {
                disabled: triggerData.entity?.disabled || false,
                name: triggerData.entity?.name || "",
                parameters: triggerData.entity?.parameters || "0 0 14 1 2 * 2025",
                runParameters: triggerData.entity?.runParameters || "",
                type: triggerData.entity?.type || 0,
                description: triggerData.entity?.description || "",
            };
            setValue('name', formData.name);
            setValue('description', formData.description);
            setValue('disabled', formData.disabled);
            setValue('parameters', formData.parameters);
            setValue('runParameters', formData.parameters);
            setValue('type', formData.type);
            reset(formData);
        }
    }, [triggerData, triggerId, reset]);
      
    // Handle job loading error
    React.useEffect(() => {
        if (triggerError) {
            logger.error(triggerError);
            toast.error("Failed to load trigger data");
        }
    }, 
    [triggerError]);
  
    const onSubmit = React.useCallback(
        async (formValues: Values): Promise<void> => {
            try {
                if (triggerId) {
                    await updateJobTrigger({
                        entityId1: `${jobId}`,
                        entityId2: `${triggerId}`,
                        atApiServiceEndpointsAdministrationModelsPutEntityRequestOfJobTriggerDtoAndJobIdAndJobTriggerId: {
                            model:{
                                ...formValues,
                                jobId
                            }
                        }
                    }).unwrap();
                    toast.success("Job Trigger updated");
                } else {
                    const trigger: AtApiServiceEndpointsAdministrationModelsJobTriggerDto = {
                        jobId: jobId,
                        name: formValues.name,
                        type: formValues.type,
                        description: formValues.description,
                        parameters: formValues.parameters,
                        runParameters: formValues.runParameters,
                        disabled: formValues.disabled,
                    }
                    await createJobTrigger({
                        jobId: `${jobId}`,
                        atApiServiceEndpointsAdministrationModelsJobTriggerDto: trigger
                    }).unwrap();
                    toast.success("Job trigger created");
                }
                navigate(paths.administration.jobs.edit(jobId, true));
            } catch (error) {
                logger.error(error);
                toast.error("Something went wrong!");
            }
        },
        [navigate, triggerId, jobId, updateJobTrigger, createJobTrigger]
    );

    return (
        <form onSubmit={handleSubmit(onSubmit)}>
            <Card>
                <Paper elevation={3} sx={{ p: 4, transition: 'all 300ms ease', }}>
                    <Stack spacing={4}>
                        <Typography variant="h5" fontWeight={600}>
                            {triggerId ? "Update trigger" : "Create new trigger for this job"}
                        </Typography>
                        <Typography variant="h6">
                            Basic info
                        </Typography>
                        <Grid
                            size={{
                                md: 6,
                                xs: 12,
                            }}
                            >
                            <Controller
                                control={control}
                                name="name"
                                render={({ field }) => (
                                    <Tooltip disableFocusListener title={<Link href="https://aristotelos.cz" target="_blank">Documentation:</Link>} arrow>
                                        <FormControl error={Boolean(errors.name)} fullWidth>
                                            <InputLabel required>Name</InputLabel>
                                            <OutlinedInput required {...field} />
                                            {errors.name ? <FormHelperText>{errors.name.message}</FormHelperText> : null}
                                        </FormControl>
                                    </Tooltip>
                                )}
                            />
                        </Grid>
                        <Grid
                            size={{
                              xs: 12,
                            }}
                            >
                            <Controller
                                control={control}
                                name="description"
                                render={({ field }) => (
                                    <Tooltip disableFocusListener title={<Link href="https://aristotelos.cz" target="_blank">Documentation:</Link>} arrow>
                                        <FormControl error={Boolean(errors.description)} fullWidth>
                                            <InputLabel>Description</InputLabel>
                                            <OutlinedInput multiline {...field} />
                                            {errors.description ? <FormHelperText>{errors.description.message}</FormHelperText> : null}
                                        </FormControl>
                                    </Tooltip>
                              )}
                            />
                        </Grid>
                        <Controller
                            control={control}
                            name="parameters"
                            render={({field})=>{
                                let val = field.value.replace(/\?/g, "*").split(/\s+/);;
                                if (isValidCronArray(val)){
                                    return (<Tooltip disableFocusListener title={<Link href="https://aristotelos.cz" target="_blank">Documentation:</Link>} arrow>
                                        <FormControl error={Boolean(errors.parameters)} fullWidth>
                                            <CronEditSelector params={val} setParams={field.onChange} creating={!triggerId} />
                                            {errors.parameters ? <FormHelperText>{errors.parameters.message}</FormHelperText> : null}
                                        </FormControl>
                                    </Tooltip>);
                                }
                                return <Typography>Incorrect trigger parameters passed.</Typography>
                            }}
                        />
                        <Stack direction="row" spacing={2} m={1}>
                            <Controller
                                control={control}
                                name="disabled"
                                render={({field})=>(
                                    <Tooltip disableFocusListener title={<Link href="https://aristotelos.cz" target="_blank">Documentation:</Link>} arrow>
                                        <FormControl error={Boolean(errors.disabled)} fullWidth>
                                            <InputLabel>Trigger enabled</InputLabel>
                                            <Switch checked={field.value} onChange={field.onChange}/>
                                            {errors.disabled ? <FormHelperText>{errors.disabled.message}</FormHelperText> : null}
                                        </FormControl>
                                    </Tooltip>
                                )}
                            />
                            <Button 
                                sx={{ml:'auto'}}
                                onClick={() => navigate(paths.administration.jobs.edit(jobId, true))}
                                >
                                Cancel
                            </Button>
                            <Button
                                type="submit" 
                                variant="contained"
                                sx={{
                                    ml:'auto',
                                    bgcolor: 'primary.main',
                                    color: 'white',
                                    padding: '6px 12px',
                                    '&:hover': {
                                        bgcolor: 'primary.dark',
                                    },
                                    borderRadius: 2,
                                }}
                                >
                                {triggerId ? "Update trigger" : "Create trigger"}
                            </Button>
                        </Stack>
                    </Stack>
                </Paper>
            </Card>
        </form>
    );
}