/** @type {import('prettier').Config} */
const config = {
	endOfLine: "lf",
	printWidth: 120,
	semi: true,
	singleQuote: false,
	tabWidth: 2,
	trailingComma: "es5",
	useTabs: true,
	importOrder: [
		"^node:$",
		"",
		"^(react/(.*)$)|^(react$)",
		"<THIRD_PARTY_MODULES>",
		"",
		"^@/types$",
		"^@/types/(.*)$",
		"^@/config$",
		"^@/config/(.*)$",
		"^@/paths$",
		"^@/routes$",
		"^@/root$",
		"^@/pages/(.*)$",
		"^@/data/(.*)$",
		"^@/lib/(.*)$",
		"^@/locales/(.*)$",
		"^@/contexts/(.*)$",
		"^@/hooks/(.*)$",
		"^@/components/(.*)$",
		"^@/styles/(.*)$",
		"",
		"^[./]",
	],
	plugins: ["@ianvs/prettier-plugin-sort-imports"],
};

export default config;
