import * as React from "react";

import { Stack } from "@mui/system";
import { styled } from "@mui/material";
import { ToggleButton, ToggleButtonGroup } from '@mui/material';

import { CronSingleDateSelector } from "./cron-single-date-selector";
import { CronErrorWrapper } from "./cron-error-wrapper";
import { Fade } from "@mui/material";
import { CronRepeatedSelector } from "./cron-repeated-selector";
import { CronArray, isValidCronArray } from "@/utils/cron";

const ANY_CRON = "*";
const defaultRepeated: CronArray = ["0", "0/2", "14-15", "*", "*" ,"*" ,"*"];
const defaultSingle: CronArray = ["0", "0", "14", "1", "2", "*", "2025"];

const DARK_GREY = '#333';
const LIGHT_GREY = '#ddd';

const PillToggleButton = styled(ToggleButton)(({ theme }) => ({
    border: '2px solid #ccc',
    padding: '6px 16px',
    fontWeight: 500,
    textTransform: 'none',
    color: DARK_GREY,
    backgroundColor: LIGHT_GREY,
    borderRadius: 0,

    '&:first-of-type': {
        borderTopLeftRadius: 50,
        borderBottomLeftRadius: 50,
    },
    '&:last-of-type': {
        borderTopRightRadius: 50,
        borderBottomRightRadius: 50,
    },

    '&.Mui-selected': {
        backgroundColor: DARK_GREY,
        color: LIGHT_GREY,
        zIndex: 1,
        '&:hover': {
            backgroundColor: theme.palette.primary.dark,
        },
    },

    '&:not(:first-of-type)': {
        marginLeft: -3, // collapse adjacent borders
    },

    '&:focus-visible': {
        outline: 'none',
    },
}));

const PillToggleGroup = styled(ToggleButtonGroup)({
    border: 'none',
    padding: 0,
    margin: 0,
    gap: 0,
    display: 'inline-flex',
    overflow: 'hidden',
    boxShadow: 'none',
});


interface CronEditSelectorProps {
    readonly params?: CronArray;
    readonly setParams: React.Dispatch<string>;
    readonly creating: boolean;
}

export function CronEditSelector({params, setParams, creating}: CronEditSelectorProps) : React.JSX.Element {
    const [repeats, setRepeats] = React.useState(false);
    const [cronRepeated, setCronRepeated] = React.useState(defaultRepeated);
    const [cronSingle, setCronSingle] = React.useState(defaultSingle);
    const [errorMsg, setErrorMsg] = React.useState<string|undefined>(undefined);

    const initialized = React.useRef(creating);
    
    React.useEffect(() => {
        if (!params){
            setErrorMsg(`Incorrect cron expression ${params} passed`);
            return;
        }

        if (initialized.current || params === undefined || params === defaultSingle) {
            return;
        }
        
        // Check the correctness of the cron received
        if (!isValidCronArray(params)){
            setErrorMsg(`Incorrect cron expression ${params} passed`);
            return;
        }

        const nextIsSingle = params[5] === ANY_CRON && params.every((val, idx) => idx === 5 || val !== ANY_CRON);
        setRepeats(!nextIsSingle);
        setCronRepeated(nextIsSingle ? defaultRepeated : params);
        setCronSingle(nextIsSingle ? params : defaultSingle);
        initialized.current = true;
    }, [params]);
    
    React.useEffect(
        () => {
            const cronString = repeats ? cronRepeated.join(' ') : cronSingle.join(' ');
            setParams(cronString);
        },
        [cronRepeated, cronSingle, repeats]
    );

    
    const handleToggleButtonChange = (
        event: React.MouseEvent<HTMLElement>,
        newValue: boolean | null
    ) => {
        if (newValue !== null) {
            setRepeats(newValue);
        }
    };

    return (
        <CronErrorWrapper
            errorMessage = {errorMsg}
            isLoading = {!initialized.current}
        >
            {/* Toggle Mode */}
            <PillToggleGroup exclusive color="primary" size="medium" value={repeats} onChange={handleToggleButtonChange}>
                <PillToggleButton value={false}>Single</PillToggleButton>
                <PillToggleButton value={true}>On repeat</PillToggleButton>
            </PillToggleGroup>
        
            {/* Input Section */}
            { repeats ? (
                <Fade in={repeats}>
            <Stack spacing={2}>
                <CronRepeatedSelector cronExpr={cronRepeated} setCronExpr={setCronRepeated}/>
            </Stack>
            </Fade>
            ) : (
                <CronSingleDateSelector cronExpr={cronSingle} setCronExpr={setCronSingle} />
            )
            }
        </CronErrorWrapper>
    );
}