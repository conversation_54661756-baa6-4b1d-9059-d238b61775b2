This is a [Vite](https://vitejs.dev/) project.

## Getting Started

First, install dependencies and run the development server:

```bash
npm run dev
# or
yarn dev
# or
pnpm dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

You can start editing the page by modifying `src/app.tsx`. The page auto-updates as you edit the file.

## Learn More

To learn more about Vite, take a look at the following resources:

- [Vite Documentation](https://vitejs.dev/guide) - learn about Vite features and API.

You can check out [the Vite GitHub repository](https://github.com/vitejs/vite) - your feedback and contributions are welcome!

## Deploy on Vercel

The easiest way to deploy your Next.js app is to use the [Vercel Platform](https://vercel.com/new) from the creators of Next.js.
