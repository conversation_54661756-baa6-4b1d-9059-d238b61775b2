{"name": "@devias-kit-pro/vite-starter", "author": "Devias IO", "version": "8.1.0", "private": true, "scripts": {"dev": "vite", "build": "tsc && vite build", "start": "vite preview", "lint": "eslint ./src", "lint:fix": "eslint --fix ./src", "format": "prettier --check \"**/*.{js,jsx,mjs,ts,tsx,mdx}\" --cache", "format:fix": "prettier --write \"**/*.{js,jsx,mjs,ts,tsx,mdx}\" --cache", "typecheck": "tsc --noEmit", "test": "jest --watch", "generate-api": "npx @rtk-query/codegen-openapi openapi-config.js"}, "dependencies": {"@auth0/auth0-react": "2.3.0", "@clerk/clerk-react": "5.31.9", "@dnd-kit/core": "6.3.1", "@dnd-kit/sortable": "10.0.0", "@dnd-kit/utilities": "3.2.2", "@emotion/cache": "11.14.0", "@emotion/react": "11.14.0", "@emotion/server": "11.11.0", "@emotion/styled": "11.14.0", "@fontsource/inter": "5.2.6", "@fontsource/plus-jakarta-sans": "5.2.6", "@fontsource/roboto-mono": "5.2.6", "@fullcalendar/core": "^6.1.17", "@fullcalendar/daygrid": "^6.1.17", "@fullcalendar/interaction": "^6.1.17", "@fullcalendar/list": "^6.1.17", "@fullcalendar/react": "^6.1.17", "@fullcalendar/timegrid": "^6.1.17", "@fullcalendar/timeline": "^6.1.17", "@hookform/resolvers": "5.1.1", "@mui/lab": "7.0.0-beta.13", "@mui/material": "^7.2.0", "@mui/system": "7.1.1", "@mui/utils": "7.1.1", "@mui/x-data-grid-generator": "^8.6.0", "@mui/x-data-grid-premium": "^8.6.0", "@mui/x-date-pickers": "8.5.1", "@phosphor-icons/react": "2.1.10", "@react-pdf/renderer": "4.3.0", "@reduxjs/toolkit": "^2.8.2", "@rtk-query/codegen-openapi": "^2.0.0", "@supabase/supabase-js": "2.50.0", "@testing-library/dom": "10.4.0", "@tiptap/extension-link": "2.14.0", "@tiptap/extension-placeholder": "2.14.0", "@tiptap/react": "2.14.0", "@tiptap/starter-kit": "2.14.0", "@vitejs/plugin-react": "4.5.2", "dayjs": "1.11.13", "embla-carousel": "8.6.0", "embla-carousel-react": "8.6.0", "highlight.js": "^11.11.1", "i18next": "25.2.1", "lowlight": "^3.3.0", "mapbox-gl": "3.12.0", "odata-filter-builder": "^1.0.0", "oidc-client-ts": "3.2.1", "openid-client": "6.5.1", "react": "19.1.0", "react-dom": "19.1.0", "react-dropzone": "14.3.8", "react-gtm-hook": "0.0.1", "react-helmet-async": "2.0.5", "react-hook-form": "7.57.0", "react-i18next": "15.5.2", "react-map-gl": "8.0.4", "react-markdown": "10.1.0", "react-oidc-context": "3.3.0", "react-redux": "^9.2.0", "react-router-dom": "7.6.2", "react-syntax-highlighter": "^15.6.6", "recharts": "2.15.3", "sass": "^1.90.0", "sonner": "2.0.5", "stylis": "4.3.6", "stylis-plugin-rtl": "2.1.1", "vite": "^6.3.5", "zod": "3.25.57"}, "devDependencies": {"@eslint/eslintrc": "3.3.1", "@eslint/js": "9.28.0", "@ianvs/prettier-plugin-sort-imports": "4.4.2", "@testing-library/jest-dom": "6.6.3", "@testing-library/react": "16.3.0", "@types/jest": "29.5.14", "@types/mapbox-gl": "3.4.1", "@types/node": "24.0.0", "@types/react": "^19.1.7", "@types/react-dom": "19.1.6", "@types/react-syntax-highlighter": "15.5.13", "@typescript-eslint/parser": "8.34.0", "eslint": "^9.35.0", "eslint-config-prettier": "10.1.5", "eslint-import-resolver-alias": "1.1.2", "eslint-import-resolver-typescript": "4.4.3", "eslint-plugin-import": "2.31.0", "eslint-plugin-react": "7.37.5", "eslint-plugin-react-hooks": "5.2.0", "eslint-plugin-unicorn": "^61.0.2", "globals": "16.2.0", "jest": "30.0.0", "jest-environment-jsdom": "30.0.0", "prettier": "3.5.3", "typescript": "5.8.3", "typescript-eslint": "8.34.0"}, "overrides": {"@auth0/auth0-react": {"react": "^16.11.0 || 17.x || 18.x || 19.x", "react-dom": "^16.11.0 || 17.x || 18.x || 19.x"}, "prismjs": "^1.30.0", "react-helmet-async": {"react": "^16.8.0 || 17.x || 18.x || 19.x", "react-dom": "^16.8.0 || 17.x || 18.x || 19.x"}}}