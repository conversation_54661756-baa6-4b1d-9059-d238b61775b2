<?xml version="1.0" encoding="UTF-8"?>
<svg width="108px" height="110px" viewBox="0 0 108 110" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>iconly-glass-tick</title>
    <defs>
        <linearGradient x1="88.0814069%" y1="16.8240929%" x2="17.137702%" y2="77.1430431%" id="linearGradient-1">
            <stop stop-color="#9BF763" offset="0%"></stop>
            <stop stop-color="#26AB5B" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="100.91953%" y1="-0.9195332%" x2="-0.611636293%" y2="100.611636%" id="linearGradient-2">
            <stop stop-color="#72DC60" offset="0%"></stop>
            <stop stop-color="#72DC60" stop-opacity="0.35" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="15.5014844%" y1="11.1810686%" x2="80.0711452%" y2="91.1524441%" id="linearGradient-3">
            <stop stop-color="#FFFFFF" stop-opacity="0.25" offset="0%"></stop>
            <stop stop-color="#FFFFFF" stop-opacity="0" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="89.7887181%" y1="16.8241305%" x2="16.8956202%" y2="79.1834028%" id="linearGradient-4">
            <stop stop-color="#9BF763" offset="0%"></stop>
            <stop stop-color="#26AB5B" offset="100%"></stop>
        </linearGradient>
        <filter x="-46.2%" y="-38.7%" width="192.3%" height="177.4%" filterUnits="objectBoundingBox" id="filter-5">
            <feGaussianBlur stdDeviation="8" in="SourceGraphic"></feGaussianBlur>
        </filter>
        <linearGradient x1="92.909942%" y1="32.9907485%" x2="-18.3766644%" y2="36.6190864%" id="linearGradient-6">
            <stop stop-color="#FFFFFF" offset="0%"></stop>
            <stop stop-color="#FFFFFF" stop-opacity="0.2" offset="100%"></stop>
        </linearGradient>
        <path d="M13.5507067,27 C12.616308,27 11.6819092,26.6466997 10.9686102,25.935999 L1.07002355,16.0736899 C-0.356674518,14.6522886 -0.356674518,12.3497864 1.07002355,10.9325851 C2.49662163,9.51118381 4.80331851,9.5069838 6.22991658,10.9283851 L13.5507067,18.2223919 L30.7700834,1.06597599 C32.1966815,-0.355325329 34.5033784,-0.355325329 35.9299764,1.06597599 C37.3566745,2.4873773 37.3566745,4.78987944 35.9299764,6.21128075 L16.1328032,25.935999 C15.4194042,26.6466997 14.4851054,27 13.5507067,27" id="path-7"></path>
        <linearGradient x1="11.1916147%" y1="41.3656374%" x2="95.1951187%" y2="41.8275957%" id="linearGradient-9">
            <stop stop-color="#FFFFFF" offset="0%"></stop>
            <stop stop-color="#FFFFFF" stop-opacity="0" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="Page-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="iconly-glass-tick" transform="translate(1.000000, 9.000000)">
            <path d="M60.8536677,0.82044707 L89.9485154,8.58020712 C100.671762,11.4402761 104.987366,19.1833532 102.149942,29.7224338 L92.5413759,65.4168903 C90.6930021,72.2834434 86.9107019,76.5644941 81.6088452,78 C81.8172296,76.5621065 81.9243701,75.0641268 81.9243701,73.512129 L81.9243701,39.8849801 C81.9243701,25.3145452 72.5376812,15.5186847 58.5195635,15.5186847 L39,15.5186847 L39.6627321,13.0566378 C42.499857,2.51762692 50.3002266,-1.99421892 60.8536677,0.82044707 Z" id="Path" fill="url(#linearGradient-1)" fill-rule="nonzero"></path>
            <path d="M23.0298905,15 L59.0117372,15 C65.9243556,15 71.6606633,17.4237486 75.6698696,21.6200445 C79.6810821,25.8184469 82,31.8298209 82,39.0674634 L82,72.9743651 C82,80.1909429 79.6812827,86.1918848 75.6700702,90.3851715 C71.6607636,94.5765523 65.9243556,97 59.0117372,97 L23.0298905,97 C16.1170715,97 10.3700309,94.576452 6.35025218,90.3847703 C2.32871803,86.1911827 0,80.190341 0,72.9743651 L0,39.0674634 C0,31.8304228 2.32894874,25.8191491 6.35046282,21.6205461 C10.3702316,17.4238489 16.1170715,15 23.0298905,15 Z" id="Path" stroke="url(#linearGradient-3)" fill="url(#linearGradient-2)" fill-rule="nonzero" stroke-linecap="round" stroke-linejoin="round"></path>
            <g id="Group" opacity="0.5" transform="translate(31.000000, 15.000000)" fill="url(#linearGradient-4)" fill-rule="nonzero">
                <path d="M51.8261815,61.4063915 C51.9412627,60.316927 52,59.1953022 52,58.0439069 L52,24.3874975 C52,9.80448392 42.6339471,0 28.646896,0 L9.5815135,0 L0.83913284,32.5760054 C-2.00050042,43.1569145 2.45655204,50.7147637 12.8489616,53.4950788 L41.500188,61.1600622 C45.3857238,62.199544 48.8534171,62.2654575 51.8261815,61.4063915 Z" id="Path" filter="url(#filter-5)"></path>
            </g>
            <g id="Group" transform="translate(23.000000, 43.000000)">
                <path d="M13.5507067,27 C12.616308,27 11.6819092,26.6466997 10.9686102,25.935999 L1.07002355,16.0736899 C-0.356674518,14.6522886 -0.356674518,12.3497864 1.07002355,10.9325851 C2.49662163,9.51118381 4.80331851,9.5069838 6.22991658,10.9283851 L13.5507067,18.2223919 L30.7700834,1.06597599 C32.1966815,-0.355325329 34.5033784,-0.355325329 35.9299764,1.06597599 C37.3566745,2.4873773 37.3566745,4.78987944 35.9299764,6.21128075 L16.1328032,25.935999 C15.4194042,26.6466997 14.4851054,27 13.5507067,27" id="Path" fill="url(#linearGradient-6)" fill-rule="nonzero"></path>
                <g id="Clipped">
                    <mask id="mask-8" fill="white">
                        <use xlink:href="#path-7"></use>
                    </mask>
                    <g id="Path"></g>
                    <path d="M1.42945,11.42905 L1.57035,11.57095 L1.57055,11.57075 L1.42945,11.42905 Z M13.50005,18.35885 L13.35885,18.50045 L13.50005,18.64115 L13.64115,18.50045 L13.50005,18.35885 Z M35.57055,6.57085 L35.42945,6.42915 L35.42945,6.42915 L35.57055,6.57085 Z M16.42945,25.42915 L16.57055,25.57085 L16.57055,25.57085 L16.42945,25.42915 Z M14,26.715424 C13.0261815,26.715424 12.0535747,26.4778031 11.3110932,26 L11,26.2015509 C11.8290677,26.7349886 12.9151397,27 14,27 L14,26.715424 Z M11,25.7207684 L1.27728121,16 L1,16.2792316 L10.7226206,26 L11,25.7207684 Z M1,15.7388748 C0.0444788947,14.5006268 0.0446915004,12.4956863 0.999858263,11.2615861 L0.800079727,11 C-0.266775922,12.3784427 -0.266563316,14.6179626 0.79993799,16 L1,15.7388748 Z M1.25935726,11 C2.49863109,10.0466919 4.50192014,10.0442093 5.74064274,10.9970209 L6,10.7960034 C4.61731253,9.73246907 2.38204436,9.73587374 1,10.7989825 L1.25935726,11 Z M6,11.2618083 L13.7029594,18 L14,17.7382841 L6.29704064,11 L6,11.2618083 Z M13.2742076,18 L30,1.27615886 L29.7257924,1 L13,17.7238411 L13.2742076,18 Z M30.2593573,1 C31.4983555,0.0455352703 33.5016445,0.0455352703 34.7406427,1 L35,0.798749734 C33.61768,-0.266249911 31.3824118,-0.266249911 30,0.798749734 L30.2593573,1 Z M36,1.26093764 C36.9553371,2.49811182 36.9553371,4.50188818 36,5.73906236 L36.2000531,6 C37.266649,4.61886341 37.266649,2.38113659 36.2000531,1 L36,1.26093764 Z M35.7188191,6 L16,25.7168147 L16.2811809,26 L36,6.28318531 L35.7188191,6 Z M16.6890056,26 C15.9463866,26.4778031 14.9738543,26.715424 14,26.715424 L14,27 C15.0849001,27 16.171012,26.7349886 17,26.2015509 L16.6890056,26 Z" id="Shape" fill-opacity="0.5" fill="url(#linearGradient-9)" fill-rule="nonzero" mask="url(#mask-8)"></path>
                </g>
            </g>
        </g>
    </g>
</svg>