<?xml version="1.0" encoding="UTF-8"?>
<svg width="116px" height="82px" viewBox="0 0 116 82" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>iconly-glass-volume</title>
    <defs>
        <linearGradient x1="29.4621347%" y1="54.6569221%" x2="92.2300757%" y2="126.546709%" id="linearGradient-1">
            <stop stop-color="#FF759F" offset="0%"></stop>
            <stop stop-color="#FF196E" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="68.5773705%" y1="78.6969701%" x2="138.340182%" y2="239.348839%" id="linearGradient-2">
            <stop stop-color="#FF759F" offset="0%"></stop>
            <stop stop-color="#FF196E" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="69.2010451%" y1="72.7423425%" x2="114.145278%" y2="178.987896%" id="linearGradient-3">
            <stop stop-color="#FF759F" offset="0%"></stop>
            <stop stop-color="#FF196E" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="15.4909253%" y1="19.3653007%" x2="71.0461968%" y2="88.2301705%" id="linearGradient-4">
            <stop stop-color="#FFFFFF" stop-opacity="0.25" offset="0%"></stop>
            <stop stop-color="#FFFFFF" stop-opacity="0" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="44.2103424%" y1="54.6569778%" x2="101.933378%" y2="101.107707%" id="linearGradient-5">
            <stop stop-color="#FF759F" offset="0%"></stop>
            <stop stop-color="#FF196E" offset="100%"></stop>
        </linearGradient>
        <filter x="-120.0%" y="-96.0%" width="340.0%" height="292.0%" filterUnits="objectBoundingBox" id="filter-6">
            <feGaussianBlur stdDeviation="8" in="SourceGraphic"></feGaussianBlur>
        </filter>
    </defs>
    <g id="Page-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="iconly-glass-volume" transform="translate(0.000000, 5.000000)" fill-rule="nonzero">
            <g id="Group-2">
                <path d="M24.6852998,7.79662869 C25.1845182,6.49439685 25.702945,5.14345446 26.432064,3.8519519 C28.2945789,0.793782133 31.5396986,-0.557813385 34.4225599,0.212106412 C36.0321641,0.635644864 37.8006378,2.16637753 38.6851247,3.47369481 L46.0981679,14.0065005 L51.1018571,15.3427748 C53.4379791,15.9666886 55.1776402,17.6517644 56,19.8810821 L51.1955981,26.7073392 L44.0498718,28.6156759 C38.790871,30.0202547 35.6484962,35.1904529 36.4175327,41.2348484 C36.5446883,42.1241026 36.7156631,43.127829 36.9229538,44.2113271 L26.2064653,49.1520822 C24.9740261,49.7514662 22.9424373,50.2736712 21.3199275,49.8403119 C21.2147815,49.8153832 21.1220409,49.790654 21.0294004,49.7659248 C18.091415,48.9812712 16.0812355,46.1436949 16,42.5760088 C16.0103045,41.0796926 16.2346026,39.7343442 16.4513975,38.4342662 L16.4553992,38.4100356 L16.6644907,37.1127496 C17.2669543,32.8846542 22.6362033,12.9123329 24.2274995,8.97996093 L24.6852998,7.79662869 Z" id="Path" fill="url(#linearGradient-1)"></path>
                <path d="M14.1972828,10.4329567 C14.9211607,9.78788294 15.9991321,9.87293781 16.6077215,10.6260092 C17.2077001,11.3810611 17.108485,12.5172177 16.3851812,13.1602031 C14.3719856,14.9534637 12.8168823,17.6502178 12.0044093,20.7575884 C11.1925103,23.8629981 11.2228393,26.9972332 12.0914736,29.5818228 C12.4040442,30.5093375 11.9357146,31.5441146 11.0458404,31.8900207 C10.7140391,32.0162059 10.3600412,32.0341483 10.0247956,31.9420831 C9.48424078,31.7936416 9.05274603,31.3746912 8.86600735,30.8226924 C7.76905395,27.5596249 7.71172542,23.6636706 8.70822953,19.8525262 C9.70473364,16.0413817 11.6552908,12.6967399 14.1972828,10.4329567 Z" id="Path" fill="url(#linearGradient-2)"></path>
                <path d="M10.122722,3.45232336 C10.8707149,2.78855259 11.9869458,2.86249752 12.605398,3.62693922 C13.2175312,4.39187196 13.1049093,5.55213301 12.354878,6.21539271 C8.81505427,9.34075115 6.06327814,14.0031301 4.60867195,19.3407137 C3.15351539,24.6803015 3.1656439,30.070796 4.64190815,34.5177135 C4.95353959,35.4627936 4.4615908,36.5210109 3.54055764,36.8819672 C3.19717803,37.0138435 2.83388321,37.0357895 2.48887288,36.944899 C1.9326022,36.7983921 1.48935091,36.3743035 1.30364198,35.8132279 C-0.40399557,30.6755631 -0.435107754,24.5085416 1.21677533,18.4471414 C2.86754747,12.3897496 6.03118344,7.06217694 10.122722,3.45232336 Z" id="Path" fill="url(#linearGradient-3)"></path>
            </g>
            <path d="M67.3926769,6.77110703 C69.2592755,6.26949332 71.2590028,6.47233 73.0921499,7.31674556 C74.8793935,8.14001626 76.5073788,9.57201909 77.7100776,11.5567438 C78.714065,13.3509257 79.4306105,15.2321391 80.1358562,17.0836393 L80.7945491,18.7963152 C81.8574865,21.4397252 84.1255095,29.2236182 86.3215698,37.369444 L86.5751072,38.3127141 L86.953241,39.730179 L87.2030751,40.6738374 C87.2859488,40.9878235 87.3684272,41.3012765 87.4503398,41.6135505 L87.6949878,42.5491584 C89.5936182,49.8332372 91.1962273,56.522697 91.5603188,59.0932396 L91.8613385,60.9732628 C92.1734028,62.8569642 92.4843126,64.7273874 92.4999521,66.8035516 C92.4441715,69.1538569 91.770185,71.2463148 90.6398883,72.8809956 C89.5079668,74.5180259 87.9181778,75.6934519 86.0446137,76.1969294 C83.597058,76.8503438 80.9586688,76.1760236 79.3240719,75.3762556 L62.0925981,67.3821316 L54.4117434,69.3226327 C51.9189823,69.9925232 49.5079,69.6753766 47.4468923,68.4900177 C45.3781949,67.3002362 43.6669814,65.2437742 42.5568313,62.4543419 C40.0816836,56.5420615 37.4260377,45.9444344 36.6073625,40.1879247 C36.2530211,37.3796379 36.7896266,34.7930634 38.014342,32.7457974 C39.2372102,30.7016193 41.1464047,29.1993776 43.5229696,28.5607573 L50.5591645,26.6698634 L61.8548509,11.0740833 C62.4364737,10.2072503 63.3156118,9.26033019 64.3201612,8.45703307 C65.3087889,7.66656435 66.3883721,7.0370236 67.3926769,6.77110703 Z M100.777497,10.5031169 C101.138824,10.4778375 101.512021,10.6072527 101.817683,10.8899988 C107.334565,15.9606886 111.612074,23.4662149 113.849321,32.0190359 C116.088996,40.5793737 116.046612,49.2691179 113.74396,56.4864325 C113.590363,56.969608 113.235801,57.3333191 112.789486,57.4556557 C112.520869,57.5294184 112.236218,57.5103506 111.965783,57.4024456 C111.570661,57.2407113 111.268879,56.915466 111.096724,56.5278285 C110.92164,56.1335949 110.879302,55.6715975 111.017405,55.235279 C113.120053,48.6349219 113.137058,40.6644967 111.078039,32.7922044 C109.018544,24.9206552 105.111482,18.0251072 100.067559,13.3853686 C99.7360464,13.079881 99.5493798,12.6591675 99.508525,12.2297623 C99.4678044,11.8017675 99.5713756,11.3613271 99.8340788,11.0188926 C100.078003,10.7052524 100.419561,10.5281591 100.777497,10.5031169 Z M93.7767983,20.5102979 C94.1253304,20.46726 94.4884134,20.5595944 94.7948794,20.7898668 C98.4261132,24.0537051 101.127054,28.7352198 102.512308,34.0855905 C103.900493,39.4469899 103.821223,44.9099675 102.305059,49.4646737 C102.142085,49.9514351 101.772554,50.3219412 101.306707,50.450997 C101.025335,50.5291668 100.726455,50.5133269 100.447976,50.4066171 C100.053105,50.251352 99.7538555,49.937446 99.5855873,49.5613303 C99.4142411,49.178335 99.3774962,48.7286144 99.5195042,48.3029875 C100.834602,44.3506403 100.880119,39.588275 99.6642282,34.8928983 C98.490549,30.3609817 96.2717741,26.387593 93.3722058,23.6423316 C92.9060395,23.2206605 92.632818,22.7464052 92.55028,22.2798492 C92.4699485,21.8257664 92.5755054,21.3846336 92.8456202,21.0408153 C93.0913552,20.7344997 93.4254317,20.5536857 93.7767983,20.5102979 Z" id="Shape" stroke="url(#linearGradient-4)" fill-opacity="0.35" fill="#FF749F"></path>
            <g id="Group" opacity="0.5" transform="translate(36.000000, 19.000000)" fill="url(#linearGradient-5)">
                <path d="M19.4788232,0 C19.9475651,1.26587 20.1149588,2.70974377 19.9202597,4.23436758 C19.3478367,8.22176968 17.4950302,15.5431075 15.7637127,19.6373905 C14.1097603,23.752303 10.5721345,25.75514 6.75294701,24.7389861 L1.80474263,23.4224263 L0.613794047,23.9695029 C0.408707121,22.9020698 0.239631599,21.9132255 0.113888482,21.0371562 C-0.646605102,15.0823804 2.4608625,9.98884011 7.66144351,8.60508682 L14.7278901,6.72504491 L19.4788232,0 Z" id="Path" filter="url(#filter-6)"></path>
            </g>
        </g>
    </g>
</svg>