import { appConfig } from "@/config/app";

const { baseUrl, tenantId, userId } = appConfig.api;

export async function getFromApi<T>(
    endpoint: string
): Promise<T> {
    return fetchFromApi<T>(endpoint, {
        method: 'GET',
    });
}

// NOTE: This is just a stub, extend this for a full version.
export async function postToApi<T>(
    endpoint: string,
    bodyObject: any
): Promise<T> {
    return fetchFromApi<T>(endpoint, {
        method: 'POST',
        body: JSON.stringify(bodyObject),
        headers: {
            'Content-Type': 'application/json',
        },
    });
}

export async function putToApi<T>(
    endpoint: string,
    bodyObject: any
): Promise<T> {
    return fetchFromApi<T>(endpoint, {
        method: 'PUT',
        body: JSON.stringify({model: bodyObject}),
        headers: {
            'Content-Type': 'application/json',
        },
    });
}

async function fetchFromApi<T>(
    endpoint: string,
    options: RequestInit = {}
): Promise<T> {
    const url = `${baseUrl}${endpoint}`;

    const headers: any = {
        ...options.headers,
    };

    if (tenantId) {
        headers['tenant-id'] = tenantId.toString();
    }
    if (userId) {
        headers['user-id'] = userId.toString();
    }

    const requestInit: RequestInit = {
        ...options,
        headers: headers,
    };

    const response = await fetch(url, requestInit);

    if (!response.ok) {
        const errorBody = await response.text();
        throw new Error(`API error: ${response.status} ${response.statusText} - ${errorBody}`);
    }

    return response.json();
}
