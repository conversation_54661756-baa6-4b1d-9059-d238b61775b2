/**
 * Asset management utilities for handling static assets with proper base path support
 */

/**
 * Get asset URL with automatic base path handling
 * Uses Vite's built-in BASE_URL which automatically includes the configured base path
 */
export function asset(path: string): string {
  // Remove leading slash if present to ensure consistent behavior
  const cleanPath = path.startsWith('/') ? path.slice(1) : path;
  
  // Vite automatically handles the base path via import.meta.env.BASE_URL
  return `${import.meta.env.BASE_URL}${cleanPath}`;
}

/**
 * Predefined asset paths for common assets
 * This provides type safety and centralized asset management
 */
export const assets = {
  // User avatars
  avatar: () => asset('assets/avatar.png'),
  avatar1: () => asset('assets/avatar-1.png'),
  avatar2: () => asset('assets/avatar-2.png'),
  avatar3: () => asset('assets/avatar-3.png'),
  avatar4: () => asset('assets/avatar-4.png'),
  avatar5: () => asset('assets/avatar-5.png'),
  avatar6: () => asset('assets/avatar-6.png'),
  avatar7: () => asset('assets/avatar-7.png'),
  avatar8: () => asset('assets/avatar-8.png'),
  avatar9: () => asset('assets/avatar-9.png'),
  avatar10: () => asset('assets/avatar-10.png'),
  avatar11: () => asset('assets/avatar-11.png'),

  // Company avatars
  companyAvatar1: () => asset('assets/company-avatar-1.png'),
  companyAvatar2: () => asset('assets/company-avatar-2.png'),
  companyAvatar3: () => asset('assets/company-avatar-3.png'),
  companyAvatar4: () => asset('assets/company-avatar-4.png'),
  companyAvatar5: () => asset('assets/company-avatar-5.png'),

  // Workspace avatars
  workspaceAvatar1: () => asset('assets/workspace-avatar-1.png'),
  workspaceAvatar2: () => asset('assets/workspace-avatar-2.png'),

  // Logos
  logo: () => asset('assets/logo.svg'),
  logoDark: () => asset('assets/logo--dark.svg'),
  logoEmblem: () => asset('assets/logo-emblem.svg'),
  logoEmblemDark: () => asset('assets/logo-emblem--dark.svg'),

  // File type icons
  iconJpg: () => asset('assets/icon-jpg.svg'),
  iconPng: () => asset('assets/icon-png.svg'),
  iconSvg: () => asset('assets/icon-svg.svg'),
  iconPdf: () => asset('assets/icon-pdf.svg'),
  iconMp4: () => asset('assets/icon-mp4.svg'),
  iconOther: () => asset('assets/icon-other.svg'),

  // Flag icons for languages
  flagUk: () => asset('assets/flag-uk.svg'),
  flagDe: () => asset('assets/flag-de.svg'),
  flagEs: () => asset('assets/flag-es.svg'),

  // Error page illustrations
  notFound: () => asset('assets/not-found.svg'),
  error: () => asset('assets/error.svg'),
} as const;


