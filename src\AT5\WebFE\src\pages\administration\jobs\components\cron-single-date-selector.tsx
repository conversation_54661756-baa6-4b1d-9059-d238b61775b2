import * as React from "react";

import { Input<PERSON><PERSON>l, Stack, Typography } from "@mui/material";

import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { TimePicker } from '@mui/x-date-pickers/TimePicker';
import { renderTimeViewClock } from '@mui/x-date-pickers/timeViewRenderers';

import type { Dayjs } from 'dayjs';
import dayjs from 'dayjs';

import { CronErrorWrapper } from "./cron-error-wrapper";
import { CronDayOfMonth, CronMinuteOptions, CronMonth, CronSingle, Hours, CronToDateTime, CronArray, CronYear } from "@/utils/cron";

const ANY_CRON = "*";

type SingleDateSelectorProps = {
    readonly cronExpr: CronArray;
    readonly setCronExpr: React.Dispatch<React.SetStateAction<CronArray>>;
}

export function CronSingleDateSelector({cronExpr, setCronExpr}: SingleDateSelectorProps): React.JSX.Element {
    const [dateTime, setDateTime] = React.useState<Dayjs|null>(dayjs());
    const [errorMsg, setErrorMsg] = React.useState<string|undefined>(undefined);
    const [cron, setCron] = React.useState<CronSingle>(new CronSingle("0", "*", "*", "*", "*", "*", "*"));

    // -------- DEPENDENCY CHECKS ------
    React.useEffect(() => {
        setErrorMsg(undefined);
        setCronExpr(cron.toCronArray());
    }, [cron]);

    // -------- INITIALIZATION ---------
    const cronExprToState = (
        cronExpr: CronArray
    ) => {
        let cronTmp = CronSingle.parseArray(cronExpr);
        if (cronTmp === undefined){
            setErrorMsg(`Incorrect cron expression ${cronExpr.join(' ')} passed`)
            return;
        }

        setCron(cronTmp);
        const dtReceived = CronToDateTime(cronTmp);
        if (dtReceived.isAfter(dateTime)){
            setDateTime(dtReceived);
        }
    };

    React.useEffect(() => {
        setErrorMsg(undefined);
        cronExprToState(cronExpr);
    }, []);

    const updateDateTimeAndCron = (
        newDate: Dayjs| null,
    ) => {
        setCron(cron.copyWith({
            minutes: `${newDate?.minute()}` as CronMinuteOptions,
            hours: `${newDate?.hour()}` as Hours,
            dayOfMonth: `${newDate?.date()}` as CronDayOfMonth,
            month: `${newDate?.month()}` as CronMonth,
            year: `${newDate?.year()}` as CronYear
        }));
        setDateTime(newDate)
    }

    return (
        <CronErrorWrapper
            errorMessage = {errorMsg}
        >
            <LocalizationProvider dateAdapter = {AdapterDayjs}>
                <InputLabel>{cronExpr.join(' ')}</InputLabel>    
                <Typography variant = "body2" color = "text.secondary">
                    Choose a specific date/time:
                </Typography>
                <Stack direction = "row" spacing = {2}>
                <DatePicker disablePast value = {dateTime} onChange = {(newDate) => updateDateTimeAndCron(newDate)} label = "Pick a date" />
                <TimePicker
                    label = "Pick a time"
                    value = {dateTime}
                    onChange = {(newTime) => updateDateTimeAndCron(newTime)}
                    viewRenderers = {{
                    hours: renderTimeViewClock,
                    minutes: renderTimeViewClock
                    }}
                />
                </Stack>
            </LocalizationProvider>
        </CronErrorWrapper>
    );
}