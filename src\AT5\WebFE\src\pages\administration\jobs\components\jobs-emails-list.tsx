"use client";

import * as React from "react";
import { Box, Button, Checkbox, FormControl, IconButton, InputLabel, OutlinedInput, Stack, Tooltip } from "@mui/material";
import { DataGridPremium, GridColDef, GridRenderCellParams } from "@mui/x-data-grid-premium";
import { CheckIcon } from "@phosphor-icons/react/dist/ssr/Check";
import { PencilSimpleIcon } from "@phosphor-icons/react/dist/ssr/PencilSimple";
import { TrashIcon } from "@phosphor-icons/react/dist/ssr/Trash";
import { useCallback, useEffect, useRef, useState } from "react";
import './jobs-emails-list.scss';

export type JobsEmail = {
    email: string;
    isSuccess: boolean;
    isFailure: boolean;
}

export type JobsEmailsListProps = {
    successEmails: string[];
    errorEmails: string[];
    onEmailsChange?: (successEmails: string[], failureEmails: string[]) => void;
};

export function JobsEmailsList({successEmails, errorEmails, onEmailsChange}: JobsEmailsListProps): React.JSX.Element {
    const [allEmails, setEmails] = useState<JobsEmail[]>([]);
    const [editingEmail, setEditingEmail] = useState<string | null>(null);
    const [newEmailError, setNewEmailError] = useState<string>("");
    const [editEmailError, setEditEmailError] = useState<string>("");

    const newEmailRef = useRef<HTMLInputElement>(null);
    const editInputRef = useRef<HTMLInputElement>(null);

    const notifyEmailsChange = useCallback((emails: JobsEmail[]) => {
        if (onEmailsChange) {
            const successEmails = emails
                .filter(email => email.isSuccess)
                .map(email => email.email);
            const failureEmails = emails
                .filter(email => email.isFailure)
                .map(email => email.email);
            onEmailsChange(successEmails, failureEmails);
        }
    }, [onEmailsChange]);

    useEffect(() => {
        const emailMap = new Map<string, JobsEmail>();

        for (const existingEmail of allEmails) {
            emailMap.set(existingEmail.email, {
                email: existingEmail.email,
                isSuccess: false,
                isFailure: false
            });
        }

        for (const email of successEmails) {
                const trimmedEmail = email.trim();
                const existingEmail = emailMap.get(trimmedEmail)
                if(existingEmail) {
                    existingEmail.isSuccess = true;
                    continue;
                }

                emailMap.set(trimmedEmail, {
                    email: trimmedEmail,
                    isSuccess: true,
                    isFailure: false
                });
        }

        for (const email of errorEmails) {
                const trimmedEmail = email.trim();
                const existingEmail = emailMap.get(trimmedEmail)
                if(existingEmail) {
                    existingEmail.isFailure = true;
                    continue;
                }

                emailMap.set(trimmedEmail, {
                    email: trimmedEmail,
                    isSuccess: false,
                    isFailure: true
                });
        }

        const deduplicatedEmails = Array.from(emailMap.values());
        setEmails(deduplicatedEmails);
    }, [successEmails, errorEmails]);

    const addEmail = () => {
        const emailValue = newEmailRef.current?.value || "";
        const validationError = validateEmail(emailValue);

        if (validationError) {
            setNewEmailError(validationError);
            return;
        }

        setNewEmailError("");

        const trimmedEmail = emailValue.trim();
        const newEmails = [...allEmails, {
            email: trimmedEmail,
            isSuccess: true,
            isFailure: true
        }];
        setEmails(newEmails);
        notifyEmailsChange(newEmails);

        if (newEmailRef.current) {
            newEmailRef.current.value = "";
        }
    };

    const handleSuccessChange = (emailAddress: string, checked: boolean) => {
        setEmails(prevEmails => {
            const newEmails = prevEmails.map(email =>
                email.email === emailAddress
                    ? { ...email, isSuccess: checked }
                    : email
            );
            notifyEmailsChange(newEmails);
            return newEmails;
        });
    };

    const handleFailureChange = (emailAddress: string, checked: boolean) => {
        setEmails(prevEmails => {
            const newEmails = prevEmails.map(email =>
                email.email === emailAddress
                    ? { ...email, isFailure: checked }
                    : email
            );
            notifyEmailsChange(newEmails);
            return newEmails;
        });
    };

    const handleEditStart = (emailAddress: string) => {
        setEditingEmail(emailAddress);
        setEditEmailError("");
        setTimeout(() => {
            if (editInputRef.current) {
                editInputRef.current.focus();
                requestAnimationFrame(() => {
                    if (editInputRef.current) {
                        editInputRef.current.select();
                    }
                });
            }
        }, 50);
    };

    const handleEditConfirm = () => {
        const newEmailValue = editInputRef.current?.value || "";
        const validationError = validateEmail(newEmailValue, true);

        if (validationError) {
            setEditEmailError(validationError);
            return;
        }

        setEditEmailError("");

        if (editingEmail && newEmailValue.trim()) {
            setEmails(prevEmails => {
                const newEmails = prevEmails.map(email =>
                    email.email === editingEmail
                        ? { ...email, email: newEmailValue.trim() }
                        : email
                );
                notifyEmailsChange(newEmails);
                return newEmails;
            });
        }
        setEditingEmail(null);
    };

    const handleEditCancel = () => {
        setEditingEmail(null);
        setEditEmailError(""); 
    };

    const handleDeleteEmail = (emailAddress: string) => {
        setEmails(prevEmails => {
            const newEmails = prevEmails.filter(email => email.email !== emailAddress);
            notifyEmailsChange(newEmails);
            return newEmails;
        });

        if (editingEmail === emailAddress) {
            setEditingEmail(null);
        }
    };

    const isValidEmail = (email: string): boolean => {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    };

    const validateEmail = (email: string, isEditing: boolean = false): string => {
        if (!email.trim()) {
            return "Email address is required";
        }

        if (!isValidEmail(email.trim())) {
            return "Please enter a valid email address";
        }

        const emailExists = allEmails.some(existingEmail =>
            existingEmail.email === email.trim() &&
            (!isEditing || existingEmail.email !== editingEmail)
        );

        if (emailExists) {
            return "This email address already exists";
        }

        return "";
    };

    const handleNewEmailChange = () => {
        if (newEmailError) {
            setNewEmailError("");
        }
    };

    const columns: GridColDef[] = [
        {
            field: 'email',
            headerName: 'Email',
            flex: 1,
            minWidth: 200,
            renderCell: (params: GridRenderCellParams) => {
                const isEditing = editingEmail === params.row.email;

                if (isEditing) {
                    return (
                        <Tooltip
                            title={editEmailError || ""}
                            open={!!editEmailError}
                            placement="bottom-start"
                            arrow
                            slotProps={{
                                tooltip: {
                                    className: 'jobs-emails-list__error-tooltip'
                                }
                            }}
                        >
                            <Box className="jobs-emails-list__edit-input-container">
                                <OutlinedInput
                                    inputRef={editInputRef}
                                    defaultValue={editingEmail}
                                    error={!!editEmailError}
                                    onChange={() => {
                                        if (editEmailError) {
                                            setEditEmailError("");
                                        }
                                    }}
                                    onKeyDown={(e) => {
                                        if (e.key === 'Enter') {
                                            e.preventDefault();
                                            e.stopPropagation();
                                            handleEditConfirm();
                                        } else if (e.key === 'Escape') {
                                            e.preventDefault();
                                            e.stopPropagation();
                                            handleEditCancel();
                                        } else if (['ArrowLeft', 'ArrowRight', 'ArrowUp', 'ArrowDown', 'Home', 'End'].includes(e.key)) {
                                            e.stopPropagation();
                                        }
                                    }}
                                    size="small"
                                    sx={{ width: '100%' }}
                                />
                            </Box>
                        </Tooltip>
                    );
                }

                return (
                    <span
                        onDoubleClick={() => handleEditStart(params.row.email)}
                        className="jobs-emails-list__email-cell"
                    >
                        {params.value}
                    </span>
                );
            }
        },
        {
            field: 'isSuccess',
            headerName: 'On Successful Completion',
            width: 180,
            align: 'center',
            headerAlign: 'center',
            renderCell: (params: GridRenderCellParams) => (
                <Checkbox
                    checked={params.value as boolean}
                    onChange={(e) => handleSuccessChange(params.row.email, e.target.checked)}
                    size="small"
                    sx={{ padding: 0 }}
                />
            )
        },
        {
            field: 'isFailure',
            headerName: 'Failure',
            width: 120,
            align: 'center',
            headerAlign: 'center',
            renderCell: (params: GridRenderCellParams) => (
                <Checkbox
                    checked={params.value as boolean}
                    onChange={(e) => handleFailureChange(params.row.email, e.target.checked)}
                    size="small"
                    sx={{ padding: 0 }}
                />
            )
        },
        {
            field: 'actions',
            headerName: '',
            width: 100,
            align: 'center',
            headerAlign: 'center',
            sortable: false,
            disableColumnMenu: true,
            renderCell: (params: GridRenderCellParams) => {
                const isEditing = editingEmail === params.row.email;

                if (isEditing) {
                    return (
                        <IconButton
                            onClick={handleEditConfirm}
                            size="small"
                            sx={{ padding: '4px' }}
                        >
                            <CheckIcon fontSize="small" />
                        </IconButton>
                    );
                }

                return (
                    <Stack direction="row" spacing={0.5} alignItems="center">
                        <IconButton
                            onClick={() => handleEditStart(params.row.email)}
                            size="small"
                            sx={{ padding: '4px' }}
                        >
                            <PencilSimpleIcon fontSize="small" />
                        </IconButton>
                        <IconButton
                            onClick={() => handleDeleteEmail(params.row.email)}
                            size="small"
                            sx={{ padding: '4px' }}
                        >
                            <TrashIcon fontSize="small" />
                        </IconButton>
                    </Stack>
                );
            }
        }
    ];


	return (
        <Stack>
            <Stack direction={"row"} spacing={2} alignItems={"end"}>
                <FormControl fullWidth error={!!newEmailError}>
                    <InputLabel>Add email</InputLabel>
                    <OutlinedInput
                        inputRef={newEmailRef}
                        error={!!newEmailError}
                        onChange={handleNewEmailChange}
                        onKeyDown={(e) => {
                            if (e.key === 'Enter') {
                                e.preventDefault();
                                e.stopPropagation();
                                addEmail();
                                setTimeout(() => {
                                    (e.target as HTMLInputElement).focus();
                                }, 0);
                            }
                        }}
                    />
                    {newEmailError && (
                        <Box className="jobs-emails-list__error-message">
                            {newEmailError}
                        </Box>
                    )}
                </FormControl>
                <Button variant="outlined" onClick={addEmail}>Add</Button>
            </Stack>
            <DataGridPremium
                rows={allEmails}
                columns={columns}
                getRowId={(row) => row.email}
                pagination
                pageSizeOptions={[5, 10, 25]}
                initialState={{
                    pagination: { paginationModel: { pageSize: 10, page: 0 } },
                }}
                disableRowSelectionOnClick
                rowHeight={52}
                className="jobs-emails-list__data-grid"
            />
        </Stack>
	);
}