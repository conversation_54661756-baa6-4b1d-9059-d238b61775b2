# Development Environment Configuration
# Copy this to .env.development.local for local development

# App
VITE_APP_URL=
VITE_APP_BASE_PATH=
VITE_LOG_LEVEL=DEBUG
VITE_AUTH_STRATEGY=NONE

# API Configuration for Development
# Backend API running on different localhost port
VITE_API_BASE_URL=https://localhost:7488
VITE_API_TENANT_ID=10
VITE_API_USER_ID=27219

# In development, not using 401 redirect to login page
VITE_NOT_AUTHORIZED_REDIRECT_PATH=

# Auth0
VITE_AUTH0_DOMAIN=
VITE_AUTH0_CLIENT_ID=

# Clerk
VITE_CLERK_PUBLISHABLE_KEY=

# Cognito
VITE_COGNITO_AUTHORITY=
VITE_COGNITO_DOMAIN=
VITE_COGNITO_CLIENT_ID=

# Supabase
VITE_SUPABASE_URL=
VITE_SUPABASE_PUBLIC_KEY=

# Mapbox
VITE_MAPBOX_API_KEY=

# Google Tag Manager
VITE_GOOGLE_TAG_MANAGER_ID=
