import { createApi } from '@reduxjs/toolkit/query/react';
import { createCustomBaseQuery } from './custom-base-query';
import { appConfig } from '@/config/app';

export const baseApi = createApi({
  reducerPath: 'api',
  baseQuery: createCustomBaseQuery(appConfig),
  // Define tag types for cache invalidation
  tagTypes: ['Job', 'JobTrigger', 'User', 'Database', 'CurrentUser', 'CurrentTenant'],
  endpoints: () => ({}),
});
