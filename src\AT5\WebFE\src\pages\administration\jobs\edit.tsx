import * as React from "react";
import Box from "@mui/material/Box";
import Link from "@mui/material/Link";
import Stack from "@mui/material/Stack";
import { ArrowLeftIcon } from "@phosphor-icons/react/dist/ssr/ArrowLeft";
import { Helmet } from "react-helmet-async";

import type { Metadata } from "@/types/metadata";
import { appConfig } from "@/config/app";
import { paths } from "@/paths";
import { RouterLink } from "@/components/core/link";

import { JobEditForm } from "@/pages/administration/jobs/components/job-edit-form";
import { Grid } from "@mui/material";
import { JobTriggersList } from "./components/job-triggers-list";

interface PageProps {
	jobId?: number;
	scrollToTriggers?: boolean;
}

export function Page({ jobId, scrollToTriggers }: PageProps): React.JSX.Element {
	const metadata = {
		title: `${jobId ? 'Edit' : 'Create'} | Jobs | Dashboard | ${appConfig.name}`
	} satisfies Metadata;

	React.useEffect(
		() => {
			if (scrollToTriggers){
				const element = document.getElementById("triggers");
				if (element){
					element.scrollIntoView({behavior: 'auto'});
				}
			}
		},
		[]
	)
	
	return (
		<React.Fragment>
			<Helmet>
				<title>{metadata.title}</title>
			</Helmet>
			<Box
				sx={{
					maxWidth: "var(--Content-maxWidth)",
					m: "var(--Content-margin)",
					p: "var(--Content-padding)",
					width: "var(--Content-width)",
				}}
			>
				<Stack spacing={4}>
					<Stack spacing={3}>
						<div>
							<Link
								color="text.primary"
								component={RouterLink}
								href={paths.administration.jobs.index()}
								sx={{ alignItems: "center", display: "inline-flex", gap: 1 }}
								variant="subtitle2"
							>
								<ArrowLeftIcon fontSize="var(--icon-fontSize-md)" />
								Browse Jobs
							</Link>
						</div>
					</Stack>
                    <JobEditForm jobId={jobId} />
					<div id="triggers">
						<Grid
							size={{
								xs: 12,
							}}>
							<Stack direction={"column"}>
								{jobId && <JobTriggersList jobId={jobId} />}
							</Stack>
						</Grid>
					</div>
				</Stack>
			</Box>
		</React.Fragment>
	);
}
