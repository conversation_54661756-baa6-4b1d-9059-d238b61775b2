import { Typography } from "@mui/material";
import * as React from "react";

interface CronErrorWrapperProps {
    children: React.ReactNode;
    errorMessage?: string;
    isLoading?: boolean;
}

export function CronErrorWrapper({children, errorMessage, isLoading}: CronErrorWrapperProps) : React.JSX.Element {
    if (errorMessage){
        return (
            <Typography>
                {errorMessage}
            </Typography>
        );
    }

    if (isLoading){
        return (
            <Typography>
                Loading...
            </Typography>
        );
    }

    return <>{children}</>;
}