import * as React from "react";
import Box from "@mui/material/Box";
import Stack from "@mui/material/Stack";
import { Helmet } from "react-helmet-async";

import type { Metadata } from "@/types/metadata";
import { appConfig } from "@/config/app";

import { JobTriggerEditForm } from "@/pages/administration/jobs/components/job-trigger-edit-form";
import { RouterLink } from "@/components/core/link";
import { paths } from "@/paths";
import { ArrowLeftIcon } from "@phosphor-icons/react";
import Link from "@mui/material/Link";

const metadata = { title: `Create | Customers | Dashboard | ${appConfig.name}` } satisfies Metadata;

interface PageProps {
	jobId: number;
    triggerId?: number;
}

export function Page({jobId, triggerId}: PageProps): React.JSX.Element {
    return (
        <React.Fragment>
            <Helmet>
                <title>{metadata.title}</title>
            </Helmet>
            <Box
                sx={{
                    maxWidth: "var(--Content-maxWidth)",
                    m: "var(--Content-margin)",
                    p: "var(--Content-padding)",
                    width: "var(--Content-width)",
                }}
            >
                <Stack spacing={4}>
                    <Stack spacing={3}>
                        <div>
                            <Link
                                color="text.primary"
                                component={RouterLink}
                                href={paths.administration.jobs.edit(jobId, true)}
                                sx={{ alignItems: "center", display: "inline-flex", gap: 1 }}
                                variant="subtitle2"
                            >
                                <ArrowLeftIcon fontSize="var(--icon-fontSize-md)" />
                                Go back
                            </Link>
                        </div>
                    </Stack>
                    <JobTriggerEditForm jobId={jobId} triggerId={triggerId}/>
                </Stack>
            </Box>
        </React.Fragment>
    );
}
