import * as React from "react";
import { Outlet } from "react-router-dom";
import type { RouteObject } from "react-router-dom";

import { Layout as DashboardLayout } from "@/components/dashboard/layout/layout";

export const route: RouteObject = {
	path: "dashboard",
	element: (
		<DashboardLayout>
			<Outlet />
		</DashboardLayout>
	),
	children: [
		{
			index: true,
			lazy: async () => {
				const { Page } = await import("@/pages/dashboard/overview");
				return { Component: Page };
			},
		},
		{
			path: "blank",
			lazy: async () => {
				const { Page } = await import("@/pages/dashboard/blank");
				return { Component: Page };
			},
		},
	],
};
