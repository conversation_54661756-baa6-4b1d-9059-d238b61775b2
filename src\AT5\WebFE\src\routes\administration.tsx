import { Outlet, useSearchParams } from "react-router-dom";
import type { RouteObject } from "react-router-dom";

import { Layout as DashboardLayout } from "@/components/dashboard/layout/layout";

function tryParseInt(value?: string, name?: string) {
  if (value === undefined) {
	return undefined;
  }
  const result = parseInt(value, 10);
  if (isNaN(result)) {
	return undefined;
  }
  return result;
}

export const route: RouteObject = {
	path: "administration",
	element: (
		<DashboardLayout>
			<Outlet />
		</DashboardLayout>
	),
	children: [
		{
			path: "jobs",
			children: [
				{
					index: true,
					loader: async ({ request }) => {
						const url = new URL(request.url);
						const focus = url.searchParams.get("focus");
						const showLogs = focus === "logs";
						return { showLogs };
					},
					lazy: async () => {
						const { Page } = await import("@/pages/administration/jobs/jobs");
						return {
							Component: Page
						};
					}
				},
				{
					path: "create",
					lazy: async () => {
						const { Page } = await import("@/pages/administration/jobs/edit");
						return {
							Component: () => <Page jobId={undefined} />
						};
					},
				},
				{
					path: "edit/:jobId",
					lazy: async () => {
						const { Page } = await import("@/pages/administration/jobs/edit");
						const { Page: NotFoundPage } = await import("@/pages/not-found");
						const { useParams } = await import("react-router-dom");
						return {
							Component: () => {
								const { jobId } = useParams<{ jobId: string }>();
								const jobIdNumber = tryParseInt(jobId, "jobId");
								const [searchParams] = useSearchParams();
				                const focus = searchParams.get("focus");
                				const scrollToTriggers = focus === "triggers";
								return jobIdNumber 
								? <Page jobId={jobIdNumber} scrollToTriggers={scrollToTriggers} />
								: <NotFoundPage/>;
							}
						};
					},
				},
				{
					path: "edit/:jobId/create-trigger",
					lazy: async () => {
						const { Page } = await import("@/pages/administration/jobs/edit-trigger");
						const { Page: NotFoundPage } = await import("@/pages/not-found");
						const { useParams } = await import("react-router-dom");
						return {
							Component: () => {
								const { jobId } = useParams<{ jobId: string }>();
								const jobIdNumber = tryParseInt(jobId, "jobId");
								return jobIdNumber 
								? <Page jobId={jobIdNumber}/>
								: <NotFoundPage/>;
							}
						};
					},
				},
				{
					path: "edit/:jobId/edit-trigger/:triggerId",
					lazy: async () => {
						const { Page } = await import("@/pages/administration/jobs/edit-trigger");
						const { Page: NotFoundPage } = await import("@/pages/not-found");
						const { useParams } = await import("react-router-dom");
						return {
							Component: () => {
								const { jobId } = useParams<{ jobId: string }>();
								const jobIdNumber = tryParseInt(jobId, "jobId");
								const { triggerId } = useParams<{ triggerId: string }>();
								const triggerIdNumber = tryParseInt(triggerId);
								return jobIdNumber && triggerIdNumber
								? <Page jobId={jobIdNumber} triggerId={triggerIdNumber}/>
								: <NotFoundPage/>;							}
						};
					},
				},
				{
					path: "job-runs/:jobRunId",
					lazy: async () => {
						const { Page } = await import("@/pages/administration/jobs/view-run");
						const { Page: NotFoundPage } = await import("@/pages/not-found");
						const { useParams } = await import("react-router-dom");
						return {
							Component: () => {
								const { jobRunId } = useParams<{ jobRunId: string }>();
								const jobRunIdNumber = tryParseInt(jobRunId, "jobRunId");
								return jobRunIdNumber
								? <Page jobRunId= {jobRunIdNumber}/>
								: <NotFoundPage/>;							
							}
						};
					},
				},
			],
		}
	],
};
