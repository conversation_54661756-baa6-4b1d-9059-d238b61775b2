/** @type {import("@rtk-query/codegen-openapi").ConfigFile} */
const config = {
  schemaFile: './v2.json', // Can be web url
  apiFile: './src/store/api/base-api.ts',
  apiImport: 'baseApi',
  outputFiles: {
    './src/store/api/generated/administration.generated.ts': {
      exportName: 'administrationGeneratedApi',
      filterEndpoints: [/administration/i],
    },
    './src/store/api/generated/users.generated.ts': {
      exportName: 'usersGeneratedApi',
      filterEndpoints: [/users/i],
    },
    './src/store/api/generated/database.generated.ts': {
      exportName: 'databaseGeneratedApi',
      filterEndpoints: [/database/i],
    },
    './src/store/api/generated/current-user.generated.ts': {
      exportName: 'currentUserGeneratedApi',
      filterEndpoints: [/currentuser/i],
    },
    './src/store/api/generated/current-tenant.generated.ts': {
      exportName: 'currentTenantGeneratedApi',
      filterEndpoints: [/curenttenant/i],
    },
    './src/store/api/generated/demo.generated.ts': {
      exportName: 'demoGeneratedApi',
      filterEndpoints: [/demo/i],
    },
  },
  exportName: 'aristoTelosApi',
  hooks: true,

};

module.exports = config;
