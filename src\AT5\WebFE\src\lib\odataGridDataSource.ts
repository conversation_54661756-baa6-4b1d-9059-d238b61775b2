import { ODataFilterBuilder } from 'odata-filter-builder';
import type { GridDataSource, GridGetRowsParams, GridGetRowsResponse, GridUpdateRowParams, GridRowId } from '@mui/x-data-grid-premium';
import { getFromApi } from '@/config/backendApi';

export function createODataGridDataSource(
  fetchEndpoint: string,
  patchEndpointGetter: ((id: GridRowId) => string | null) = () => null,
  getSelectFields?: () => string[] | undefined,
): GridDataSource {
  return {
    getRows: async (params: GridGetRowsParams): Promise<GridGetRowsResponse> => {
      const queryParams = new URLSearchParams();

      queryParams.append('$count', 'true');

      // Handle pagination ($top and $skip)
      if (params.paginationModel) {
        queryParams.append('$top', params.paginationModel.pageSize.toString());
        queryParams.append('$skip', (params.paginationModel.page * params.paginationModel.pageSize).toString());
      }

      // Handle sorting ($orderby)
      if (params.sortModel?.length > 0) {
        const orderByParts = params.sortModel.map(sort => {
          return sort.sort === 'desc' ? `${sort.field} desc` : sort.field;
        });
        queryParams.append('$orderby', orderByParts.join(','));
      }

      // Handle filtering ($filter) - robust handling for v7 filter model shape and operator aliases
      const filterModel: any = params?.filterModel ?? {};
      const items: Array<{ field: string; operator?: string; operatorValue?: string; value?: any }>
        = Array.isArray(filterModel.items) ? filterModel.items : [];

      if (items.length > 0) {
        const builder = ODataFilterBuilder();

        for (const rawItem of items) {
          const op = (rawItem.operator ?? (rawItem as any).operatorValue ?? '').toString();
          const field = rawItem.field;
          const value = rawItem.value;

          // Skip items that require a value but don't have one
          const operatorRequiresValue = !['isEmpty', 'isNotEmpty'].includes(op);
          if (operatorRequiresValue && (value === undefined || value === null || value === '')) {
            continue;
          }

          switch (op) {
            // Equality
            case 'equals':
            case 'equal':
            case 'is':
            case '=':
            case '==':
              builder.eq(field, value);
              break;

            // Inequality
            case 'not':
            case 'isNot':
            case 'notEqual':
            case '!=':
              builder.ne(field, value);
              break;

            // String contains/starts/ends
            case 'contains':
              builder.contains(field, value);
              break;
            case 'startsWith':
              builder.startsWith(field, value);
              break;
            case 'endsWith':
              builder.endsWith(field, value);
              break;

            // Set membership
            case 'isAnyOf':
              if (Array.isArray(value)) builder.in(field, value);
              break;

            // Numeric / date comparisons (aliases included)
            case 'after':
            case '>':
              builder.gt(field, value);
              break;
            case 'onOrAfter':
            case '>=':
              builder.ge(field, value);
              break;
            case 'before':
            case '<':
              builder.lt(field, value);
              break;
            case 'onOrBefore':
            case '<=':
              builder.le(field, value);
              break;

            // Empty checks
            case 'isEmpty':
              builder.eq(field, null);
              break;
            case 'isNotEmpty':
              builder.ne(field, null);
              break;
          }
        }

        const filterString = builder.toString();
        if (filterString) queryParams.append('$filter', filterString);
      }

      // Handle field selection ($select) - use columns from the Columns menu (visible, non-action columns)
      try {
        const selectedFields = getSelectFields?.();
        if (selectedFields && selectedFields.length > 0) {
          const uniqueFields = Array.from(new Set(selectedFields.filter(Boolean)));
          // Ensure 'id' is included for row identification
          if (!uniqueFields.includes('id')) uniqueFields.unshift('id');
          queryParams.append('$select', uniqueFields.join(','));
        }
      } catch {
        // Ignore select field errors and fall back to server defaults
      }

      const fullEndpoint = `${fetchEndpoint}?${queryParams.toString()}`;

      const responseData = await getFromApi<any>(fullEndpoint);

      // const responseData = {
      //   value: {
      //     items: [
      //       { id: 1, name: 'Přehled zápůjček', type: 'ReportJob' },
      //       { id: 2, name: 'Statistika denních účtů ACC_FIN2', type: 'ReportJob' },
      //       { id: 3, name: 'Nemoc za org. jednotku k datu - 49 dnů a více', type: 'ReportJob' },
      //     ],
      //   },
      //   count: 3,
      // };

      return {
          rows: responseData.value ?? [],
          rowCount: responseData.count ?? responseData.value?.length ?? 0,
      };
    },

    updateRow: async (params: GridUpdateRowParams): Promise<any> => {
      const patchUrl = patchEndpointGetter(params.rowId);
      if (!patchUrl) {
        return Promise.resolve();
      }

      // Only include changed fields
      const changedFields: Record<string, any> = {};
      const { updatedRow, previousRow } = params;
      for (const key in updatedRow) {
        if (updatedRow.hasOwnProperty(key)) {
          if (updatedRow[key] !== previousRow[key]) {
            changedFields[key] = updatedRow[key];
          }
        }
      }

      console.log('Changed fields:', changedFields);

      // const response = await fetch(patchUrl, {
      //   method: 'PATCH',
      //   headers: {
      //     'Content-Type': 'application/json',
      //   },
      //   body: JSON.stringify(changedFields),
      // });
      
      return Promise.resolve();
    },
  };
} 