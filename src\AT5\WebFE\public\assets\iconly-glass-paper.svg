<?xml version="1.0" encoding="UTF-8"?>
<svg width="102px" height="111px" viewBox="0 0 102 111" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>iconly-glass-paper</title>
    <defs>
        <linearGradient x1="25.0126345%" y1="11.1701835%" x2="81.2232254%" y2="80.6344021%" id="linearGradient-1">
            <stop stop-color="#FFFFFF" stop-opacity="0.25" offset="0%"></stop>
            <stop stop-color="#FFFFFF" stop-opacity="0" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="71.7756876%" y1="0%" x2="71.7756876%" y2="100.498122%" id="linearGradient-2">
            <stop stop-color="#BC94FF" offset="0%"></stop>
            <stop stop-color="#9F66FF" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="71.7755548%" y1="0%" x2="71.7755548%" y2="100.49819%" id="linearGradient-3">
            <stop stop-color="#BC94FF" offset="0%"></stop>
            <stop stop-color="#9F66FF" offset="100%"></stop>
        </linearGradient>
        <filter x="-44.4%" y="-38.1%" width="188.9%" height="176.2%" filterUnits="objectBoundingBox" id="filter-4">
            <feGaussianBlur stdDeviation="8" in="SourceGraphic"></feGaussianBlur>
        </filter>
        <linearGradient x1="93.5318841%" y1="22.0278998%" x2="-19.4536232%" y2="25.7156808%" id="linearGradient-5">
            <stop stop-color="#FFFFFF" offset="0%"></stop>
            <stop stop-color="#FFFFFF" stop-opacity="0.2" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="15.4242754%" y1="16.4162349%" x2="74.4315217%" y2="89.5793985%" id="linearGradient-6">
            <stop stop-color="#FFFFFF" stop-opacity="0.25" offset="0%"></stop>
            <stop stop-color="#FFFFFF" stop-opacity="0" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="92.9099945%" y1="18.0580825%" x2="-18.4804989%" y2="21.6900745%" id="linearGradient-7">
            <stop stop-color="#FFFFFF" offset="0%"></stop>
            <stop stop-color="#FFFFFF" stop-opacity="0.2" offset="100%"></stop>
        </linearGradient>
        <path d="M8.09599952,19.9682806 C10.8936994,19.9954805 14.7831991,20.0071805 18.0825989,19.9954805 C19.7724988,19.9915805 20.6314988,18.0353848 19.4593989,16.8569874 C15.2206991,12.5867968 7.63849955,4.94481357 3.29939981,0.573423164 C2.09929988,-0.636074181 0,0.196223992 0,1.87242031 L0,12.1239978 C0,16.4252884 3.65669978,19.9682806 8.09599952,19.9682806" id="path-8"></path>
        <linearGradient x1="17.2262309%" y1="13.1196476%" x2="78.5507863%" y2="89.0905922%" id="linearGradient-10">
            <stop stop-color="#FFFFFF" stop-opacity="0.25" offset="0%"></stop>
            <stop stop-color="#FFFFFF" stop-opacity="0" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="Page-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="iconly-glass-paper" transform="translate(9.000000, 6.000000)">
            <path d="M33.1241068,18 L62.9641439,18 L84,40.5784171 L84,80.8153989 C84,90.8594509 75.9225772,99 65.9563654,99 L33.9679612,99 C23.5195182,99 15,90.454461 15,79.9244209 L15,36.265499 C15,26.2214469 23.1176589,18 33.1241068,18 Z" id="Path" fill-opacity="0.35" fill="#BA90FF" fill-rule="nonzero"></path>
            <path d="M33.6232893,19 L63.2462659,19 L84,41.2748867 L84,81.3157368 C84,91.0881079 76.142129,99 66.4571869,99 L34.4671859,99 C24.2985837,99 16,90.6817101 16,80.4247453 L16,36.7651623 C16,26.9920903 23.8988115,19 33.6232893,19 Z" id="Path" stroke="url(#linearGradient-1)" stroke-linecap="round" stroke-linejoin="round"></path>
            <path d="M18.0437349,0 C8.07742276,0 0,8.18085672 0,18.2747657 L0,62.2333123 C0,71.46915 6.48955161,79.1859559 15.0871339,81 C15.0858295,80.9076326 15.0852274,80.8150639 15.0852274,80.7223946 L15.0852274,36.8466567 C15.0852274,26.7528484 23.2028863,18.4905916 33.2092338,18.4905916 L63.0493714,18.4905916 L69,24.9092226 L69,19.1701664 C69,8.58795763 60.4804818,0 50.0320388,0 L18.0437349,0 Z" id="Path" fill="url(#linearGradient-2)" fill-rule="nonzero"></path>
            <path d="M69,24.4276194 L69,62.7000307 C69,72.8078156 60.9097232,81 50.9277756,81 L18.8888643,81 C17.5574689,81 16.2572279,80.860854 15.00201,80.5960634 C15.0007035,80.5034673 15,80.4107705 15,80.3179729 L15,36.3815828 C15,26.2736972 23.130476,18 33.1527232,18 L63.0398753,18 L69,24.4276194 Z" id="Path" fill="url(#linearGradient-3)" fill-rule="nonzero" filter="url(#filter-4)"></path>
            <path d="M35.9301093,50 L50.0698907,50 C51.6718649,50 53,51.372392 53,53.021654 C53,54.6670377 51.6754939,56 50.0698907,56 L35.9301093,56 C34.3245061,56 33,54.6670377 33,53.021654 C33,51.372392 34.3281351,50 35.9301093,50 Z M58.1331345,76 L35.8668655,76 C34.2994685,76 33,74.627608 33,72.978346 C33,71.3329623 34.2959177,70 35.8668655,70 L58.1331345,70 C59.7040823,70 61,71.3329623 61,72.978346 C61,74.627608 59.7005315,76 58.1331345,76 Z" id="Shape" stroke="url(#linearGradient-6)" stroke-width="0.4" fill="url(#linearGradient-5)" fill-rule="nonzero"></path>
            <g id="Group" transform="translate(58.000000, 23.000000)">
                <path d="M8.09599952,19.9682806 C10.8936994,19.9954805 14.7831991,20.0071805 18.0825989,19.9954805 C19.7724988,19.9915805 20.6314988,18.0353848 19.4593989,16.8569874 C15.2206991,12.5867968 7.63849955,4.94481357 3.29939981,0.573423164 C2.09929988,-0.636074181 0,0.196223992 0,1.87242031 L0,12.1239978 C0,16.4252884 3.65669978,19.9682806 8.09599952,19.9682806" id="Path" fill="url(#linearGradient-7)" fill-rule="nonzero"></path>
                <g id="Clipped">
                    <mask id="mask-9" fill="white">
                        <use xlink:href="#path-8"></use>
                    </mask>
                    <g id="Path"></g>
                    <path d="M18.5007,20.7 L18.4998,20.3 L18.4993,20.3 L18.5007,20.7 Z M19.64195,17.35895 L19.35805,17.64075 L19.35835,17.64105 L19.64195,17.35895 Z M3.64195,1.3591 L3.35805,1.6409 L3.35805,1.6409 L3.64195,1.3591 Z M9,20.8841403 C11.8018695,20.9113403 15.6962239,20.9230403 19,20.9113403 L18.9970976,20.1113403 C15.6966243,20.1230403 11.8057727,20.1113403 9.00770624,20.0841403 L9,20.8841403 Z M19.0016365,21 C20.7384726,20.995184 21.6735993,18.5187143 20.430898,17 L19.9423617,17.5906926 C20.7187486,18.5394441 20.1741409,20.1591897 19,20.1624352 L19.0016365,21 Z M20,17.4312883 C15.6893794,13.1193314 7.98979543,5.41407474 3.57703942,1 L3,1.56871171 C7.40655675,5.97663113 15.1183359,13.6940975 19.4229606,18 L20,17.4312883 Z M4,0.60800052 C2.55691118,-0.667404584 4.17443857e-14,0.191290104 4.17443857e-14,2 L0.803333835,2 C0.803333835,0.856512099 2.46251946,0.249323106 3.42973339,1.10423112 L4,0.60800052 Z M0.1,2 L0.1,12 L0.9,12 L0.9,2 L0.1,2 Z M1.14575016e-13,13 C1.14575016e-13,17.3997429 4.07605932,21 9,21 L9,20.2237061 C4.51864407,20.2237061 0.847457627,16.9480368 0.847457627,13 L1.14575016e-13,13 Z" id="Shape" fill="url(#linearGradient-10)" fill-rule="nonzero" mask="url(#mask-9)"></path>
                </g>
            </g>
        </g>
    </g>
</svg>