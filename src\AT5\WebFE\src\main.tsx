import * as React from "react";
import { createRoot } from "react-dom/client";
import { createBrowserRouter, Outlet, RouterProvider } from "react-router-dom";

import { routes } from "@/routes";
import { Root } from "@/root";
import { ScrollRestoration } from "@/components/core/scroll-restoration";
import { getRouterBasename } from "@/lib/base-path";

const root = createRoot(document.querySelector("#root")!);

const router = createBrowserRouter([
	{
		path: "/",
		element: (
			<Root>
				<ScrollRestoration />
				<Outlet />
			</Root>
		),
		children: [...routes],
	},
],
{
	basename: getRouterBasename()
});

root.render(
	<React.StrictMode>
		<RouterProvider router={router} />
	</React.StrictMode>
);
