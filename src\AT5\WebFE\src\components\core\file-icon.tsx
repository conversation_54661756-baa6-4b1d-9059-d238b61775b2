import type * as React from "react";
import Box from "@mui/material/Box";

import { assets } from "@/lib/assets";

export interface FileIconProps {
	extension?: string;
}

export function FileIcon({ extension }: FileIconProps): React.JSX.Element {
	let icon: string;

	switch (extension?.toLowerCase()) {
		case 'jpg':
		case 'jpeg':
			icon = assets.iconJpg();
			break;
		case 'png':
			icon = assets.iconPng();
			break;
		case 'svg':
			icon = assets.iconSvg();
			break;
		case 'pdf':
			icon = assets.iconPdf();
			break;
		case 'mp4':
			icon = assets.iconMp4();
			break;
		default:
			icon = assets.iconOther();
			break;
	}

	return (
		<Box
			sx={{
				alignItems: "center",
				display: "inline-flex",
				flex: "0 0 auto",
				justifyContent: "center",
				width: "48px",
				height: "48px",
			}}
		>
			<Box alt="File" component="img" src={icon} sx={{ height: "100%", width: "auto" }} />
		</Box>
	);
}
