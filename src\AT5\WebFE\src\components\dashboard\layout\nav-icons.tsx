import type { Icon } from "@phosphor-icons/react/dist/lib/types";
import { AddressBookIcon } from "@phosphor-icons/react/dist/ssr/AddressBook";
import { AlignLeftIcon } from "@phosphor-icons/react/dist/ssr/AlignLeft";
import { CalendarCheckIcon } from "@phosphor-icons/react/dist/ssr/CalendarCheck";
import { ChartPieIcon } from "@phosphor-icons/react/dist/ssr/ChartPie";
import { ChatsCircleIcon } from "@phosphor-icons/react/dist/ssr/ChatsCircle";
import { CreditCardIcon } from "@phosphor-icons/react/dist/ssr/CreditCard";
import { CubeIcon } from "@phosphor-icons/react/dist/ssr/Cube";
import { CurrencyEthIcon } from "@phosphor-icons/react/dist/ssr/CurrencyEth";
import { EnvelopeSimpleIcon } from "@phosphor-icons/react/dist/ssr/EnvelopeSimple";
import { FileIcon } from "@phosphor-icons/react/dist/ssr/File";
import { FileDashedIcon } from "@phosphor-icons/react/dist/ssr/FileDashed";
import { FileXIcon } from "@phosphor-icons/react/dist/ssr/FileX";
import { GearIcon } from "@phosphor-icons/react/dist/ssr/Gear";
import { GraduationCapIcon } from "@phosphor-icons/react/dist/ssr/GraduationCap";
import { HouseIcon } from "@phosphor-icons/react/dist/ssr/House";
import { KanbanIcon } from "@phosphor-icons/react/dist/ssr/Kanban";
import { LinkIcon } from "@phosphor-icons/react/dist/ssr/Link";
import { LockIcon } from "@phosphor-icons/react/dist/ssr/Lock";
import { ReadCvLogoIcon } from "@phosphor-icons/react/dist/ssr/ReadCvLogo";
import { ReceiptIcon } from "@phosphor-icons/react/dist/ssr/Receipt";
import { ShareNetworkIcon } from "@phosphor-icons/react/dist/ssr/ShareNetwork";
import { ShoppingBagOpenIcon } from "@phosphor-icons/react/dist/ssr/ShoppingBagOpen";
import { ShoppingCartSimpleIcon } from "@phosphor-icons/react/dist/ssr/ShoppingCartSimple";
import { SignOutIcon } from "@phosphor-icons/react/dist/ssr/SignOut";
import { TextAlignLeftIcon } from "@phosphor-icons/react/dist/ssr/TextAlignLeft";
import { TranslateIcon } from "@phosphor-icons/react/dist/ssr/Translate";
import { TruckIcon } from "@phosphor-icons/react/dist/ssr/Truck";
import { UploadIcon } from "@phosphor-icons/react/dist/ssr/Upload";
import { UsersIcon } from "@phosphor-icons/react/dist/ssr/Users";
import { WarningDiamondIcon } from "@phosphor-icons/react/dist/ssr/WarningDiamond";

export const icons = {
	"address-book": AddressBookIcon,
	"align-left": AlignLeftIcon,
	"calendar-check": CalendarCheckIcon,
	"chart-pie": ChartPieIcon,
	"chats-circle": ChatsCircleIcon,
	"credit-card": CreditCardIcon,
	"currency-eth": CurrencyEthIcon,
	"envelope-simple": EnvelopeSimpleIcon,
	"file-dashed": FileDashedIcon,
	"file-x": FileXIcon,
	"graduation-cap": GraduationCapIcon,
	"read-cv-logo": ReadCvLogoIcon,
	"share-network": ShareNetworkIcon,
	"shopping-bag-open": ShoppingBagOpenIcon,
	"shopping-cart-simple": ShoppingCartSimpleIcon,
	"sign-out": SignOutIcon,
	"text-align-left": TextAlignLeftIcon,
	"warning-diamond": WarningDiamondIcon,
	cube: CubeIcon,
	file: FileIcon,
	gear: GearIcon,
	house: HouseIcon,
	kanban: KanbanIcon,
	link: LinkIcon,
	lock: LockIcon,
	receipt: ReceiptIcon,
	translate: TranslateIcon,
	truck: TruckIcon,
	upload: UploadIcon,
	users: UsersIcon,
} as Record<string, Icon>;
