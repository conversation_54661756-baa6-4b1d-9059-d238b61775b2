import * as React from "react";
import Box from '@mui/material/Box';
import FormControl from "@mui/material/FormControl";
import Grid from "@mui/material/Grid";
import InputLabel from "@mui/material/InputLabel";
import { MenuList } from "@mui/material";
import type { MenuProps } from "@mui/material";
import { OutlinedInput } from "@mui/material";
import Select from "@mui/material/Select";
import type { SelectChangeEvent } from "@mui/material/Select";
import { Slider } from "@mui/material";
import Stack from "@mui/material/Stack";
import { styled } from "@mui/material";
import Typography from "@mui/material/Typography";

import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { renderTimeViewClock } from '@mui/x-date-pickers/timeViewRenderers';
import { TimeClock } from "@mui/x-date-pickers/TimeClock";
import { TimePicker } from '@mui/x-date-pickers/TimePicker';
import { MenuItem } from "@mui/material";
import dayjs from "dayjs";
import type { Dayjs } from 'dayjs';

import { CronErrorWrapper } from "./cron-error-wrapper";

import { Periodicity, CronRepeated, isValidCronMinuteOptions, isValidHours, isValidCronDayOfMonth, isValidCronDayOfWeek, CronMinuteOptions, Hours, CronDayOfMonth, CronToDateTime, CronArray, CronYear } from "@/utils/cron";

// ------------------ PERIODICITY ------------------
type PeriodicityOptionProps = {
    value: Periodicity;
    label: string;
};

const periodicityOptions = [
    { value: "YEAR", label: 'Year' },
    { value: "MONTH", label: 'Month' },
    { value: "WEEK", label: 'Week' },
    { value: "DAY", label: 'Day' },
    { value: "HOUR", label: 'Hour' },
    { value: "MINUTES", label: 'X Minutes' },
] satisfies PeriodicityOptionProps[];

// ----------------- DAY OF MONTH SELECTOR ---------------
const ITEM_HEIGHT = 48;
const ITEM_PADDING_TOP = 8;
const MenuProps = {
    PaperProps: {
        style: {
            maxHeight: ITEM_HEIGHT * 4.5 + ITEM_PADDING_TOP,
            width: 250,
        },
},
};

const GridMenuList = styled(MenuList)({
    display: 'grid',
    gridTemplateColumns: 'repeat(7, 1fr)', // 7 columns
    gap: 8,
    padding: 8,
    maxHeight: ITEM_HEIGHT * 6 + ITEM_PADDING_TOP,
    overflowY: 'auto',
});

const gridMenuProps: Partial<MenuProps> = {
    PaperProps: {
        style: {
            width: 350,
            maxHeight: ITEM_HEIGHT * 6 + ITEM_PADDING_TOP,
        },
    },
    MenuListProps: {
        component: GridMenuList,
    },
};

type DayOfMonth =
    1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10
    | 11 | 12 | 13 | 14 | 15 | 16 | 17 | 18 | 19 | 20
    | 21 | 22 | 23 | 24 | 25 | 26 | 27 | 28 | 29 | 30
    | 31;

const dayOfMonthOptions = Array.from({ length: 31 }, (_, i) => i + 1 as DayOfMonth) satisfies DayOfMonth[];

type DayOfMonthSelectorProps = {
    value: DayOfMonth;
    onChange: (event: SelectChangeEvent<DayOfMonth>) => void;
};

function DayOfMonthSelector({ value, onChange }: DayOfMonthSelectorProps): React.JSX.Element{
    return (
        <FormControl>
        <InputLabel>On</InputLabel>
        <Select
            value = {value}
            onChange = {onChange}
            input = {<OutlinedInput label = "Chip" />}
            MenuProps = {gridMenuProps}
        >
            {dayOfMonthOptions.map((day) => (
            <MenuItem
                key = {day}
                value = {day}
            >
                {day}
            </MenuItem>
            ))}
        </Select>
        </FormControl>
    );
}

// ----------------- DAY OF WEEK SELECTOR ---------------
type DayOfWeek = 1 | 2 | 3 | 4 | 5 | 6 | 7;

type DayOfWeekOptionProps = {
    value: DayOfWeek;
    label: string;
};

const dayOfWeekOptions = [
    { value: 1, label: 'Sun' },
    { value: 2, label: 'Mon' },
    { value: 3, label: 'Tue' },
    { value: 4, label: 'Wed' },
    { value: 5, label: 'Thu' },
    { value: 6, label: 'Fri' },
    { value: 7, label: 'Sat' }
] satisfies DayOfWeekOptionProps[];

type DayOfWeekSelectorProps = {
    value: DayOfWeek;
    onChange: (event: SelectChangeEvent<DayOfWeek>) => void;
};

function DayOfWeekSelector({ value, onChange }: DayOfWeekSelectorProps): React.JSX.Element{
    return (
      <FormControl>
      <InputLabel>On</InputLabel>
      <Select
          value = {value}
          onChange = {onChange}
          input = {<OutlinedInput label="Chip" />}
          MenuProps = {MenuProps}
      >
          {dayOfWeekOptions.map((day) => (
          <MenuItem
              key = {day.value}
              value = {day.value}
          >
              {day.label}
          </MenuItem>
          ))}
      </Select>
      </FormControl>
    );
}

// --------- TIME RANGE SLIDER --------------
type TimeRangeSliderProps = {
    range: [number, number],
    handleChange: (event: Event, newValue: number | number[]) => void;
}


function TimeRangeSlider({ range, handleChange }: TimeRangeSliderProps): React.JSX.Element {
    return (
        <Box sx = {{ width: 400, p: 2 }}>
            <Typography gutterBottom>
                Active hours: {range[0]} - {range[1]}
            </Typography>

            <Slider
                value = {range}
                onChange = {handleChange}
                valueLabelDisplay = "auto"
                min = {0}
                max = {24}
                step = {1}
                marks = {[
                    { value: 0, label: '00' },
                    { value: 6, label: '06' },
                    { value: 12, label: '12' },
                    { value: 18, label: '18' },
                    { value: 24, label: '24' },
                ]}
            />
        </Box>
    );
}

// -------------- X MINUTES SELECTOR ------------
type XMinuteSelectorProps = {
    x: number;
    onXChange: (event: React.ChangeEvent<HTMLInputElement| HTMLTextAreaElement>) => void;
    start: number;
    onStartChange: (event: React.ChangeEvent<HTMLInputElement| HTMLTextAreaElement>) => void;
    timeRange: [number, number];
    onRangeChange: (event: Event, newValue: number | number[]) => void;
};

function XMinutesSelector({
    x,
    onXChange,
    start,
    onStartChange,
    timeRange,
    onRangeChange,
}: XMinuteSelectorProps): React.JSX.Element{
    return (
        <Stack spacing = {2} direction = "column">
            <Stack spacing = {2} direction = "row">
                <Grid>
                    <FormControl>
                        <InputLabel >Trigger every</InputLabel>
                        <OutlinedInput value = {x} onChange = {onXChange}/>
                        <Typography variant = "subtitle1" sx = {{fontWeight: 100, color: "GrayText"}}>minute{x > 1 && <>s</>}</Typography>
                    </FormControl>
                </Grid>
                <Grid>
                    <FormControl>
                        <InputLabel >Starting on</InputLabel>
                        <OutlinedInput value = {start} onChange = {onStartChange}/>
                    </FormControl>
                </Grid>
            </Stack>
            <TimeRangeSlider range = {timeRange} handleChange = {onRangeChange}/>
        </Stack>
    );
}

// ---------------- DAY & MONTH SELECTOR ---------
interface DayAndMonthSelectorProps {
    dateTime: Dayjs | null;
    setDateTime: React.Dispatch<React.SetStateAction<Dayjs | null>>;
    label: string;
}

const DayAndMonthSelector: React.FC<DayAndMonthSelectorProps> = ({ dateTime, setDateTime, label }) => {
    return(
        <LocalizationProvider dateAdapter = {AdapterDayjs}>
            <DatePicker views = {['month', 'day']} slotProps = {{
                calendarHeader: {
                format: "MMM",
                },
            }} 
            value = {dateTime} 
            onChange = {(newDate) => setDateTime(newDate)} label = {label} />
        </LocalizationProvider>
    );
}

// --------------- MINUTE & HOUR SELECTOR ---------
const HourAndMinuteSelector: React.FC<DayAndMonthSelectorProps> = ({ dateTime, setDateTime, label }) => {
    return (
        <LocalizationProvider dateAdapter = {AdapterDayjs}>
            <TimePicker
                label = {label}
                value = {dateTime}
                onChange = {(newTime) => setDateTime(newTime)}
                viewRenderers = {{
                hours: renderTimeViewClock,
                minutes: renderTimeViewClock
                }}
            />
        </LocalizationProvider>
    );
}

// --------------- MINUTE SELECTOR ---------
const OnlyMinuteSelector: React.FC<DayAndMonthSelectorProps> = ({ dateTime, setDateTime, label }) => {
    return (
        <LocalizationProvider dateAdapter = {AdapterDayjs}>
            <InputLabel>At HH:{dateTime?.minute().toString().padStart(2, '0')}</InputLabel>
            <InputLabel>{label}</InputLabel>
            <Stack direction = "row">
                <TimeClock
                    value = {dateTime}
                    onChange = {(newTime) => setDateTime(newTime)}
                    views = {["minutes"]}
                />
            </Stack>
        </LocalizationProvider>
    );
}


// ----------------- CRON SELECTOR ---------------
type CronSelectorProps = {
    readonly cronExpr: CronArray;
    readonly setCronExpr: React.Dispatch<React.SetStateAction<CronArray>>;
};

export function CronRepeatedSelector({cronExpr, setCronExpr}: CronSelectorProps): React.JSX.Element{
    const [dateTime, setDateTime] = React.useState<Dayjs|null>(dayjs());
    const [errorMsg, setErrorMsg] = React.useState<string|undefined>(undefined);
    const [cron, setCron] = React.useState<CronRepeated>(new CronRepeated(["0", "*", "*", "*", "*", "*", "*"], "MINUTES", "0", "0", ["0", "24"]));

    // -------- DEPENDENCY CHECKS ------
    React.useEffect(() => {
        setErrorMsg(undefined);
        setCronExpr(cron.toCronArray());
    }, [cron]);

    React.useEffect(() => {
        setCron(cron.copyWith({
            minutes: `${dateTime?.minute()}` as CronMinuteOptions,
            hours: `${dateTime?.hour()}` as Hours,
            dayOfMonth: `${dateTime?.date()}` as CronDayOfMonth,
            year: `${dateTime?.year()}` as CronYear
        }));
    }, [dateTime]);
    
    // -------- INITIALIZATION ---------
    const cronExprToState = (
        cronExpr: CronArray
    ) => {
        let cronTmp = CronRepeated.parseArray(cronExpr);
        if (cronTmp === undefined){
            setErrorMsg(`Incorrect cron expression ${cronExpr} passed`)
            return;
        }

        setCron(cronTmp);
        setDateTime(CronToDateTime(cronTmp));
    };
    
    React.useEffect(() => {
            setErrorMsg(undefined);
            cronExprToState(cronExpr);
    },  []);

    // -------- HANDLERS ---------
    const handlePeriodicityChange = (event: SelectChangeEvent<Periodicity>) => {
        setCron(cron.copyWith({periodicity: event.target.value}));
    };
    
    const handleXChange = (event: React.ChangeEvent<HTMLInputElement|HTMLTextAreaElement>) => {
        const numericValue = Number(event.target.value)
        if (numericValue<0) {
            setCron(cron.copyWith({x: "0"}));
            return;
        }

        let xVal = `${numericValue}`;

        if (isNaN(numericValue) || !isValidCronMinuteOptions(xVal)) {
            setCron(cron.copyWith({x: "0"}));
            return;
        }
        
        setCron(cron.copyWith({x: xVal}));
    };
    
    const handleStartChange = (event: React.ChangeEvent<HTMLInputElement|HTMLTextAreaElement>) => {
        const numericValue = Number(event.target.value)
        if (numericValue<0) {
            setCron(cron.copyWith({start: "0"}));
            return;
        }

        if (numericValue>59) {
            setCron(cron.copyWith({start: "0"}));
            return;
        }

        let startVal = `${numericValue}`;

        if (isNaN(numericValue) || !isValidCronMinuteOptions(startVal)) {
            setCron(cron.copyWith({start: "0"}));
            return;
        }
        
        setCron(cron.copyWith({start: startVal}));
    };
    
    const handleTimeRangeChange = (event: Event, newValue: number | number[]) => {
        if (!Array.isArray(newValue)){
            let hoursVal = `${newValue}`;
            if (!isValidHours(hoursVal)){
                setCron(cron.copyWith({timeRange: ["0", "24"]}));
                return;
            }

            setCron(cron.copyWith({timeRange: [hoursVal, hoursVal]}));
            return;
        }
        
        if (newValue.length != 2){
            setCron(cron.copyWith({timeRange: ["0", "24"]}));
            return;
        }

        let timeRangeVals = [`${newValue[0]}`, `${newValue[1]}`];

        if (!isValidHours(timeRangeVals[0]) || !isValidHours(timeRangeVals[1])){
            setCron(cron.copyWith({timeRange: ["0", "24"]}));
            return;
        }

        setCron(cron.copyWith({timeRange: [timeRangeVals[0], timeRangeVals[1]]}));
    };
    
    const handleDayOfMonthChange = (event: SelectChangeEvent<DayOfMonth>) => {
        let newValue = `${event.target.value}`;
        if (!isValidCronDayOfMonth(newValue)){
            setCron(cron.copyWith({dayOfMonth: "1"}));
            setDateTime(dateTime?.date(1) ?? null);
            return;
        }

        setCron(cron.copyWith({dayOfMonth: newValue}));
        setDateTime(dateTime?.date(event.target.value) ?? null);
    };
    
    const handleDayOfWeekChange = (event: SelectChangeEvent<DayOfWeek>) => {
        let newValue = `${event.target.value}`;
        if (!isValidCronDayOfWeek(newValue)){
            setCron(cron.copyWith({dayOfWeek: "1"}));
            return;
        }

        setCron(cron.copyWith({dayOfWeek: newValue}));
    };
    
    // ------ RENDERING ------
    return (
        <CronErrorWrapper
            errorMessage = {errorMsg}
        >
            <Box>
                <Stack spacing = {2} direction="column">
                    <InputLabel>{cronExpr.join(' ')}</InputLabel>
                    <InputLabel sx = {{fontWeight: 900, fontSize: '1.2rem'}}>Every</InputLabel>      
                    <Select value = {cron.periodicity} onChange = {handlePeriodicityChange} sx = {{ height: 48 }}>
                        {periodicityOptions.map((option)=>
                            (<MenuItem key = {option.value} value = {option.value}>{option.label}</MenuItem>))}
                    </Select>
                    {cron.periodicity === "YEAR" && 
                    <DayAndMonthSelector dateTime = {dateTime} setDateTime = {setDateTime} label = "On"/>}
                    {cron.periodicity === "MONTH" && 
                    <DayOfMonthSelector value = {Number(cron.dayOfMonth) as DayOfMonth} onChange = {handleDayOfMonthChange}/>}
                    {cron.periodicity === "WEEK" && 
                    <DayOfWeekSelector value = {Number(cron.dayOfWeek) as DayOfWeek} onChange = {handleDayOfWeekChange}/>}
                    {["DAY", "WEEK", "MONTH", "YEAR"].includes(cron.periodicity) && 
                    <HourAndMinuteSelector dateTime = {dateTime} setDateTime = {setDateTime} label = "At"/>}
                    {cron.periodicity === "HOUR" && 
                    <OnlyMinuteSelector dateTime = {dateTime} setDateTime = {setDateTime} label = "(choose minute)"/>}
                    {cron.periodicity === "MINUTES" && 
                    <XMinutesSelector x = {Number(cron.x)} onXChange = {handleXChange} start = {Number(cron.start)} 
                    onStartChange = {handleStartChange} timeRange = {[Number(cron.timeRange[0]), Number(cron.timeRange[1])]} 
                    onRangeChange = {handleTimeRangeChange}/>}
                </Stack>
            </Box>
        </CronErrorWrapper>
    );
}
